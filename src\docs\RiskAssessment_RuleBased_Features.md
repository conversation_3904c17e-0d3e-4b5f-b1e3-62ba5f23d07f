# 基于规则的风险评分区域功能完善文档

## 功能概述

本次完善了RiskAssessment.qml中基于规则的风险评分区域（代码行388-572）的功能，实现了完整的数据过滤、风险计算和交互功能。

## 主要功能特性

### 1. 数据类型筛选功能
- **ComboBox选择器**: 支持按数据类型筛选风险数据
  - 所有数据资产
  - PII数据（个人身份信息）
  - 财务数据
  - 健康数据
  - 商业机密
- **智能数据类型检测**: 基于文件路径自动识别数据类型
- **实时过滤**: 选择变更时自动更新显示结果

### 2. 风险重新计算功能
- **重新计算按钮**: 触发完整的风险评估流程
- **计算状态显示**: 显示"计算中..."状态，防止重复操作
- **异步处理**: 不阻塞UI界面
- **错误处理**: 处理计算过程中的异常情况

### 3. 自定义评分规则功能
- **规则配置入口**: 提供自定义评分规则的接口
- **扩展性设计**: 为未来的规则定制功能预留接口

### 4. 风险评分表格功能
- **动态数据源**: 从RiskAssessmentEngine获取实时数据
- **智能排序**: 按风险评分从高到低排序
- **数量限制**: 最多显示20个最高风险项目
- **详细信息显示**: 
  - 数据资产名称
  - 数据类型
  - 风险评分
  - 风险级别
  - 操作按钮

### 5. 交互操作功能
- **详情查看**: 点击"详情"按钮查看文件详细风险信息
- **缓解措施**: 点击"缓解措施"按钮获取针对性的风险缓解建议
- **统计显示**: 实时显示当前过滤结果的数量

## 技术实现

### QML端实现
1. **属性扩展**:
   ```qml
   property string selectedDataType: "所有数据资产"
   property var filteredRiskData: []
   property var customRiskRules: ({})
   property bool isCalculatingRisk: false
   ```

2. **核心函数**:
   - `updateFilteredRiskData()`: 更新过滤的风险数据
   - `updateRiskListModel()`: 更新ListView模型
   - `recalculateRisk()`: 重新计算风险
   - `showRiskDetails()`: 显示风险详情
   - `showMitigationMeasures()`: 显示缓解措施

3. **信号连接**:
   - 监听风险引擎的评估完成信号
   - 监听数据类型选择变更
   - 处理计算错误信号

### C++端实现
1. **新增方法**:
   ```cpp
   Q_INVOKABLE QVariantList getFilteredRiskData(const QString& dataType = "所有数据资产") const;
   Q_INVOKABLE QVariantMap getRiskDetailsForFile(const QString& filePath) const;
   Q_INVOKABLE QStringList getMitigationMeasuresForRisk(const QString& riskLevel) const;
   ```

2. **数据类型检测**:
   ```cpp
   QString detectDataTypeFromPath(const QString& filePath) const;
   ```

3. **增强的数据保存**:
   - 保存基于规则的风险评分相关信息
   - 记录计算方法和支持的数据类型

## 调试信息

### 控制台输出
- 所有关键操作都有详细的调试日志
- 错误处理和异常情况记录
- 数据流转过程追踪

### 调试信息示例
```
[RiskAssessment] 更新过滤的风险数据，选择的数据类型: PII数据
[RiskAssessmentEngine] 获取过滤的风险数据，数据类型: PII数据
[RiskAssessmentEngine] 过滤后的数据数量: 5
[RiskAssessment] 从引擎获取的过滤数据数量: 5
[RiskAssessment] 更新ListView模型，数据数量: 5
```

## 数据结构

### 过滤数据格式
```json
{
  "name": "customer_database.db",
  "type": "PII",
  "score": 95,
  "level": "高风险",
  "filePath": "C:/data/customer_database.db",
  "sensitiveItemCount": 150
}
```

### 风险详情格式
```json
{
  "filePath": "C:/data/customer_database.db",
  "fileName": "customer_database.db",
  "fileSize": 1048576,
  "lastModified": "2024-12-24T12:00:00",
  "dataType": "PII",
  "riskScore": 95.5,
  "riskLevel": "高风险",
  "riskFactors": [
    "高风险敏感数据项数量较多",
    "文件包含关键敏感信息"
  ]
}
```

## 使用说明

1. **数据类型筛选**: 在下拉框中选择要查看的数据类型
2. **重新计算**: 点击"重新计算风险"按钮刷新评估结果
3. **查看详情**: 点击表格中的"详情"按钮查看文件详细信息
4. **获取建议**: 点击"缓解措施"按钮获取风险缓解建议
5. **自定义规则**: 点击"自定义评分规则"按钮（功能待扩展）

## 扩展性

- 支持添加新的数据类型
- 可扩展自定义评分规则
- 支持更多的风险缓解措施
- 可添加更多的文件详情信息

## 注意事项

- 确保RiskAssessmentEngine已正确设置
- 需要有有效的扫描结果才能显示风险数据
- 大量数据时会自动限制显示数量以保证性能
- 所有操作都有错误处理和日志记录
