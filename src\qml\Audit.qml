import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

ScrollView {
    id: root
    anchors.fill: parent
    contentWidth: availableWidth

    ColumnLayout {
        width: parent.width
        anchors.margins: 20
        spacing: 20

        // 页面标题
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")

            RowLayout {
                anchors.fill: parent
                anchors.margins: 8

                Text {
                    text: "📊 审计与报告"
                    font.pixelSize: 24
                    font.bold: true
                    color: Qt.color("#2c3e50")
                }

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "生成审计报告"
                    Material.background: Qt.color("#27ae60")
                    Material.foreground: Qt.color("white")
                    onClicked:
                    // 生成审计报告
                    {}
                }
            }
        }

        // Windows事件日志集成区域
        GroupBox {
            Layout.fillWidth: true
            title: "Windows事件日志集成"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 事件日志配置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "日志源:"
                        font.bold: true
                    }

                    CheckBox {
                        text: "系统日志"
                        checked: true
                    }

                    CheckBox {
                        text: "安全日志"
                        checked: true
                    }

                    CheckBox {
                        text: "应用程序日志"
                        checked: false
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Button {
                        text: "同步日志"
                        Material.background: Qt.color("#3498db")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 同步Windows事件日志
                        {}
                    }
                }

                // 日志统计
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    GridLayout {
                        anchors.fill: parent
                        anchors.margins: 16
                        columns: 4

                        Text {
                            text: "今日事件"
                            font.bold: true
                        }
                        Text {
                            text: "2,456"
                            color: Qt.color("#3498db")
                        }
                        Text {
                            text: "安全事件"
                            font.bold: true
                        }
                        Text {
                            text: "127"
                            color: Qt.color("#e74c3c")
                        }
                        Text {
                            text: "登录事件"
                            font.bold: true
                        }
                        Text {
                            text: "89"
                            color: Qt.color("#f39c12")
                        }
                        Text {
                            text: "系统错误"
                            font.bold: true
                        }
                        Text {
                            text: "15"
                            color: Qt.color("#e67e22")
                        }
                    }
                }

                // 最近事件列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(200, eventListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: eventListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                time: "2024-01-15 14:35:22"
                                eventId: "4624"
                                source: "Security"
                                level: "信息"
                                description: "账户成功登录"
                            }
                            ListElement {
                                time: "2024-01-15 14:34:18"
                                eventId: "4625"
                                source: "Security"
                                level: "警告"
                                description: "账户登录失败"
                            }
                            ListElement {
                                time: "2024-01-15 14:33:45"
                                eventId: "1000"
                                source: "Application"
                                level: "错误"
                                description: "应用程序崩溃"
                            }
                            ListElement {
                                time: "2024-01-15 14:32:30"
                                eventId: "7036"
                                source: "System"
                                level: "信息"
                                description: "服务状态变更"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: "时间"
                                    font.bold: true
                                    Layout.preferredWidth: 140
                                }
                                Text {
                                    text: "事件ID"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "来源"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "级别"
                                    font.bold: true
                                    Layout.preferredWidth: 60
                                }
                                Text {
                                    text: "描述"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: eventDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 30
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: eventDelegateItem.model.time
                                    Layout.preferredWidth: 140
                                    font.family: "Consolas"
                                }
                                Text {
                                    text: eventDelegateItem.model.eventId
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: eventDelegateItem.model.source
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: eventDelegateItem.model.level
                                    Layout.preferredWidth: 60
                                    color: eventDelegateItem.model.level === "错误" ? Qt.color("#e74c3c") : (eventDelegateItem.model.level === "警告" ? Qt.color("#f39c12") : Qt.color("#3498db"))
                                }
                                Text {
                                    text: eventDelegateItem.model.description
                                    Layout.fillWidth: true
                                }
                            }
                        }
                    }
                }
            }
        }

        // 合规性报告生成区域
        GroupBox {
            Layout.fillWidth: true
            title: "合规性报告生成"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 报告配置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "报告类型:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["GDPR合规报告", "PIPL合规报告", "数据安全报告", "风险评估报告", "自定义报告"]
                        currentIndex: 1
                    }

                    Text {
                        text: "时间范围:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 120
                        model: ["本月", "本季度", "本年", "自定义"]
                        currentIndex: 0
                    }

                    Button {
                        text: "生成报告"
                        Material.background: Qt.color("#27ae60")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 生成合规性报告
                        {}
                    }
                }

                // 报告模板
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(120, reportTemplate.implicitHeight + 32)
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        id: reportTemplate
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 8

                        Text {
                            text: "PIPL合规报告模板"
                            font.bold: true
                            font.pixelSize: 16
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 4

                                Text {
                                    text: "✓ 数据处理活动记录"
                                    color: Qt.color("#27ae60")
                                }
                                Text {
                                    text: "✓ 个人信息保护措施"
                                    color: Qt.color("#27ae60")
                                }
                                Text {
                                    text: "✓ 数据主体权利履行情况"
                                    color: Qt.color("#27ae60")
                                }
                                Text {
                                    text: "✓ 数据安全事件记录"
                                    color: Qt.color("#27ae60")
                                }
                            }

                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 4

                                Text {
                                    text: "✓ 第三方处理者管理"
                                    color: Qt.color("#27ae60")
                                }
                                Text {
                                    text: "✓ 跨境传输记录"
                                    color: Qt.color("#27ae60")
                                }
                                Text {
                                    text: "✓ 风险评估结果"
                                    color: Qt.color("#27ae60")
                                }
                                Text {
                                    text: "✓ 整改措施实施"
                                    color: Qt.color("#27ae60")
                                }
                            }
                        }
                    }
                }

                // 历史报告列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(150, reportListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: reportListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "2024年1月PIPL合规报告"
                                type: "PIPL合规"
                                date: "2024-01-15"
                                size: "2.3 MB"
                                status: "已完成"
                            }
                            ListElement {
                                name: "2023年Q4数据安全报告"
                                type: "数据安全"
                                date: "2024-01-05"
                                size: "1.8 MB"
                                status: "已完成"
                            }
                            ListElement {
                                name: "2023年度风险评估报告"
                                type: "风险评估"
                                date: "2023-12-31"
                                size: "4.1 MB"
                                status: "已完成"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 25
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 6

                                Text {
                                    text: "报告名称"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "类型"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "生成日期"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "大小"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: backupDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 30
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 6

                                Text {
                                    text: backupDelegateItem.model.name
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: backupDelegateItem.model.type
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: backupDelegateItem.model.date
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: backupDelegateItem.model.size
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: backupDelegateItem.model.status
                                    Layout.preferredWidth: 80
                                    color: Qt.color("#27ae60")
                                }
                            }
                        }
                    }
                }
            }
        }

        // 取证支持区域
        GroupBox {
            Layout.fillWidth: true
            title: "取证支持"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "取证类型:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["数据泄露调查", "访问违规调查", "系统入侵调查", "内部威胁调查"]
                        currentIndex: 0
                    }

                    Button {
                        text: "开始取证"
                        Material.background: Qt.color("#e74c3c")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 开始取证流程
                        {}
                    }

                    Button {
                        text: "取证工具"
                        onClicked:
                        // 打开取证工具
                        {}
                    }
                }

                // 取证数据收集
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(150, forensicDataCollection.implicitHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        id: forensicDataCollection
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 12

                        Text {
                            text: "取证数据收集范围"
                            font.bold: true
                            font.pixelSize: 16
                        }

                        GridLayout {
                            Layout.fillWidth: true
                            columns: 4

                            CheckBox {
                                text: "系统日志"
                                checked: true
                            }
                            CheckBox {
                                text: "应用程序日志"
                                checked: true
                            }
                            CheckBox {
                                text: "网络流量日志"
                                checked: false
                            }
                            CheckBox {
                                text: "文件访问记录"
                                checked: true
                            }
                            CheckBox {
                                text: "用户活动记录"
                                checked: true
                            }
                            CheckBox {
                                text: "数据库操作日志"
                                checked: true
                            }
                            CheckBox {
                                text: "邮件通信记录"
                                checked: false
                            }
                            CheckBox {
                                text: "外设使用记录"
                                checked: false
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: "时间范围:"
                                font.bold: true
                            }

                            TextField {
                                Layout.preferredWidth: 120
                                text: "2024-01-01"
                                placeholderText: "开始日期"
                            }

                            Text {
                                text: "至"
                            }

                            TextField {
                                Layout.preferredWidth: 120
                                text: "2024-01-15"
                                placeholderText: "结束日期"
                            }

                            Item {
                                Layout.fillWidth: true
                            }

                            Button {
                                text: "收集数据"
                                Material.background: Qt.color("#9b59b6")
                                Material.foreground: Qt.color("white")
                                onClicked:
                                // 收集取证数据
                                {}
                            }
                        }
                    }
                }
            }
        }

        // 导出报表区域
        GroupBox {
            Layout.fillWidth: true
            title: "导出报表"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 导出配置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "导出格式:"
                        font.bold: true
                    }

                    CheckBox {
                        text: "PDF"
                        checked: true
                    }

                    CheckBox {
                        text: "Excel"
                        checked: true
                    }

                    CheckBox {
                        text: "Word"
                        checked: false
                    }

                    CheckBox {
                        text: "CSV"
                        checked: false
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Button {
                        text: "批量导出"
                        Material.background: Qt.color("#f39c12")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 批量导出报表
                        {}
                    }
                }

                // 导出任务列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(150, exportTaskView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: exportTaskView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "月度合规报告"
                                format: "PDF"
                                progress: 100
                                status: "已完成"
                                size: "2.3 MB"
                            }
                            ListElement {
                                name: "数据访问日志"
                                format: "Excel"
                                progress: 75
                                status: "导出中"
                                size: "1.2 MB"
                            }
                            ListElement {
                                name: "风险评估详情"
                                format: "Word"
                                progress: 0
                                status: "等待中"
                                size: "-"
                            }
                            ListElement {
                                name: "事件统计报表"
                                format: "CSV"
                                progress: 100
                                status: "已完成"
                                size: "856 KB"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: "报表名称"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "格式"
                                    font.bold: true
                                    Layout.preferredWidth: 60
                                }
                                Text {
                                    text: "进度"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "大小"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "操作"
                                    font.bold: true
                                    Layout.preferredWidth: 70
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: exportDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0

                                Text {
                                    text: exportDelegateItem.model.name
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: exportDelegateItem.model.format
                                    Layout.preferredWidth: 60
                                }
                                ProgressBar {
                                    Layout.preferredWidth: 80
                                    value: exportDelegateItem.model.progress / 100
                                }
                                Text {
                                    text: exportDelegateItem.model.status
                                    Layout.preferredWidth: 80
                                    color: exportDelegateItem.model.status === "已完成" ? Qt.color("#27ae60") : (exportDelegateItem.model.status === "导出中" ? Qt.color("#f39c12") : Qt.color("#95a5a6"))
                                }
                                Text {
                                    text: exportDelegateItem.model.size
                                    Layout.preferredWidth: 80
                                }
                                RowLayout {
                                    Layout.preferredWidth: 100

                                    Button {
                                        text: exportDelegateItem.model.status === "已完成" ? "下载" : "取消"
                                        flat: true
                                        Material.foreground: exportDelegateItem.model.status === "已完成" ? Qt.color("#27ae60") : Qt.color("#e74c3c")
                                    }
                                }
                            }
                        }
                    }
                }

                // 导出统计
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 60
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 16

                        Text {
                            text: "本月导出:"
                            font.bold: true
                        }
                        Text {
                            text: "45 个文件"
                            color: Qt.color("#3498db")
                        }

                        Text {
                            text: "总大小:"
                            font.bold: true
                        }
                        Text {
                            text: "127.8 MB"
                            color: Qt.color("#3498db")
                        }

                        Item {
                            Layout.fillWidth: true
                        }

                        Text {
                            text: "存储位置:"
                            font.bold: true
                        }
                        Text {
                            text: "C:\\Reports\\"
                            color: Qt.color("#7f8c8d")
                            font.family: "Consolas"
                        }

                        Button {
                            text: "打开文件夹"
                            flat: true
                            Material.foreground: Qt.color("#3498db")
                            onClicked:
                            // 打开报表文件夹
                            {}
                        }
                    }
                }
            }
        }

        Item {
            Layout.preferredHeight: 20
        }
    }
}
