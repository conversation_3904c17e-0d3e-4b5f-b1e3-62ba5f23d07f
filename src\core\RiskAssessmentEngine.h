#ifndef RISKASSESSMENTENGINE_H
#define RISKASSESSMENTENGINE_H

#include <QObject>
#include <QVariantMap>
#include <QVariantList>
#include <QDateTime>
#include <QMutex>
#include <QTimer>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QFile>
#include <QDir>
#include <QCoreApplication>
#include <QDebug>

class DataDiscoveryEngine;

// 风险评估结果结构
struct RiskAssessmentResult {
    // 基本信息
    QString assessmentId;           // 评估ID
    QDateTime assessmentTime;       // 评估时间
    QString version;                // 版本号

    // 总体风险评分 (0-100)
    double totalRiskScore;          // 总风险评分
    QString riskLevel;              // 风险等级 (低/中/高/极高)

    // 详细风险分析
    QVariantMap dataTypeRisks;      // 按数据类型的风险分析
    QVariantMap riskLevelCounts;    // 按风险等级的统计
    QVariantMap fileRisks;          // 按文件的风险分析
    QVariantMap directoryRisks;     // 按目录的风险分析

    // 风险趋势数据
    QVariantList riskTrends;        // 风险趋势历史数据

    // 建议和措施
    QStringList recommendations;    // 风险缓解建议
    QStringList criticalIssues;     // 关键问题列表

    // 统计信息
    int totalSensitiveFiles;        // 敏感文件总数
    int totalSensitiveItems;        // 敏感数据项总数
    double averageRiskPerFile;      // 平均每文件风险值

    RiskAssessmentResult() : totalRiskScore(0.0), totalSensitiveFiles(0),
                           totalSensitiveItems(0), averageRiskPerFile(0.0) {
        assessmentTime = QDateTime::currentDateTime();
        version = "1.0";
    }
};

// 风险评估历史记录（仅保存总评分用于趋势图）
struct RiskAssessmentHistory {
    QString assessmentId;
    QDateTime assessmentTime;
    double totalRiskScore;
    QString riskLevel;

    RiskAssessmentHistory() : totalRiskScore(0.0) {}
    RiskAssessmentHistory(const QString& id, const QDateTime& time,
                         double score, const QString& level)
        : assessmentId(id), assessmentTime(time), totalRiskScore(score), riskLevel(level) {}
};

// 风险评估引擎主类
class RiskAssessmentEngine : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isAssessing READ isAssessing NOTIFY isAssessingChanged)
    Q_PROPERTY(QVariantMap currentAssessment READ currentAssessment NOTIFY currentAssessmentChanged)
    Q_PROPERTY(QVariantList assessmentHistory READ assessmentHistory NOTIFY assessmentHistoryChanged)
    Q_PROPERTY(double totalRiskScore READ totalRiskScore NOTIFY totalRiskScoreChanged)
    Q_PROPERTY(QString riskLevel READ riskLevel NOTIFY riskLevelChanged)

public:
    explicit RiskAssessmentEngine(QObject *parent = nullptr);
    ~RiskAssessmentEngine();

    // 属性访问器
    bool isAssessing() const { return m_isAssessing; }
    QVariantMap currentAssessment() const;
    QVariantList assessmentHistory() const;
    double totalRiskScore() const { return m_currentResult.totalRiskScore; }
    QString riskLevel() const { return m_currentResult.riskLevel; }

    // 设置DataDiscoveryEngine引用
    void setDataDiscoveryEngine(DataDiscoveryEngine* engine);

public slots:
    // 风险评估控制
    Q_INVOKABLE void startRiskAssessment();
    Q_INVOKABLE void refreshAssessment();

    // 结果管理
    Q_INVOKABLE QVariantMap getDetailedAssessment() const;
    Q_INVOKABLE QVariantList getRiskTrends() const;
    Q_INVOKABLE QStringList getRecommendations() const;
    Q_INVOKABLE QStringList getCriticalIssues() const;

    // 数据管理
    Q_INVOKABLE bool saveAssessmentResults();
    Q_INVOKABLE bool loadAssessmentResults();
    Q_INVOKABLE void clearAssessmentHistory();

    // 基于规则的风险评分功能
    Q_INVOKABLE QVariantList getFilteredRiskData(const QString& dataType = "所有数据资产") const;
    Q_INVOKABLE QVariantMap getRiskDetailsForFile(const QString& filePath) const;

    // 合规性检查功能
    Q_INVOKABLE QVariantMap performComplianceCheck(const QString& standard) const;
    Q_INVOKABLE QVariantList getComplianceStandards() const;
    Q_INVOKABLE QVariantMap getComplianceHistory() const;

signals:
    void isAssessingChanged();
    void currentAssessmentChanged();
    void assessmentHistoryChanged();
    void totalRiskScoreChanged();
    void riskLevelChanged();
    void assessmentCompleted();
    void assessmentError(const QString& error);

private slots:
    // DataDiscoveryEngine信号处理
    void onDataDiscoveryScanCompleted();
    void onDataDiscoveryResultsChanged();

private:
    // 核心评估方法
    void performRiskAssessment();
    void calculateTotalRiskScore();
    void analyzeDataTypeRisks();
    void analyzeFileRisks();
    void analyzeDirectoryRisks();
    void identifyCriticalIssues();

    // 风险计算辅助方法
    double calculateDataTypeRisk(const QString& dataType, const QVariantList& items);
    double calculateFileRisk(const QString& filePath, const QVariantList& items);
    double calculateDirectoryRisk(const QString& dirPath, const QVariantList& items);
    QString determineRiskLevel(double score);
    QString convertIntToRiskLevelString(int riskLevel) const;
    QString detectDataTypeFromPath(const QString& filePath) const;

    // 历史管理
    void addToHistory();
    void maintainHistoryLimit();

    // 合规性检查私有方法
    QVariantMap checkPIPLCompliance(const QVariantList& scanResults, const QVariantMap& scanStats) const;
    QVariantMap checkGDPRCompliance(const QVariantList& scanResults, const QVariantMap& scanStats) const;
    QVariantMap checkCCPACompliance(const QVariantList& scanResults, const QVariantMap& scanStats) const;
    QVariantMap checkHIPAACompliance(const QVariantList& scanResults, const QVariantMap& scanStats) const;
    QVariantMap checkPCIDSSCompliance(const QVariantList& scanResults, const QVariantMap& scanStats) const;
    QVariantMap checkCustomCompliance(const QVariantList& scanResults, const QVariantMap& scanStats) const;

    // 合规检查辅助方法
    int calculateDataCollectionScore(const QVariantList& scanResults) const;
    int calculateDataStorageScore(const QVariantList& scanResults) const;
    int calculateSubjectRightsScore(const QVariantList& scanResults) const;
    int calculateCrossBorderScore(const QVariantList& scanResults) const;
    int calculateProcessingRecordScore(const QVariantList& scanResults) const;
    QVariantMap createRequirement(const QString& name, int score, const QString& description) const;
    void generateComplianceIssuesAndRecommendations(const QVariantList& requirements,
                                                   QStringList& issues,
                                                   QStringList& recommendations) const;

    // 文件操作
    QString getAssessmentResultsFilePath() const;
    QString getAssessmentHistoryFilePath() const;
    bool ensureDataDirectoryExists() const;

    // 数据转换
    QVariantMap resultToVariant(const RiskAssessmentResult& result) const;
    RiskAssessmentResult variantToResult(const QVariantMap& variant) const;
    QVariantMap historyToVariant(const RiskAssessmentHistory& history) const;
    RiskAssessmentHistory variantToHistory(const QVariantMap& variant) const;

private:
    DataDiscoveryEngine* m_dataDiscoveryEngine;
    RiskAssessmentResult m_currentResult;
    QList<RiskAssessmentHistory> m_assessmentHistory;

    bool m_isAssessing;
    QTimer* m_refreshTimer;

    mutable QMutex m_resultMutex;
    mutable QMutex m_historyMutex;

    // 配置常量
    static const int MAX_HISTORY_COUNT = 5;
    static constexpr double HIGH_RISK_THRESHOLD = 70.0;
    static constexpr double MEDIUM_RISK_THRESHOLD = 40.0;
    static constexpr double LOW_RISK_THRESHOLD = 20.0;
};

#endif // RISKASSESSMENTENGINE_H
