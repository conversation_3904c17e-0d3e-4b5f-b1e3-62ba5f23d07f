# RiskAssessment功能实现完整方案

## 📋 项目概述
基于Qt6.5.3和C++的数据保护平台风险评估模块实现，与现有DataDiscoveryEngine集成。

## 🎯 核心目标
1. 实现基于规则的风险评分计算
2. 提供多法规标准的合规性检查
3. 支持数据保护影响评估(DPIA)
4. 与DataDiscoveryEngine深度集成
5. 生成专业的风险评估报告

## 🏗️ 架构设计

### 核心类结构
```
RiskAssessmentEngine (主引擎)
├── AssetManager (数据资产管理)
├── RuleManager (评分规则管理)
├── ComplianceStandardManager (合规标准管理)
├── RiskScoreCalculator (风险评分计算)
├── ComplianceChecker (合规性检查)
└── ImpactAssessmentEngine (影响评估)
```

### 数据存储结构
```
/src/data/
├── assets.json              # 数据资产信息
├── rules/
│   ├── assetRules.json      # 风险评分规则
│   └── standards.json       # 合规标准定义
├── assessments/             # 影响评估报告
└── reports/                 # 生成的风险报告
```

## 📊 数据模型设计

### 1. 数据资产模型 (Asset)
```cpp
struct Asset {
    QString id;                    // 唯一标识
    QString name;                  // 资产名称
    QString type;                  // 资产类型 (PII/Financial/Health/BusinessSecret)
    int riskScore;                 // 风险评分 (0-100)
    RiskLevel riskLevel;           // 风险级别 (Low/Medium/High)
    QString description;           // 描述
    QString location;              // 存储位置
    QDateTime lastAssessed;        // 最后评估时间
    QStringList appliedRules;      // 应用的规则列表
    QVariantMap metadata;          // 扩展元数据
};
```

### 2. 评分规则模型 (ScoringRule)
```cpp
struct ScoringRule {
    QString id;                    // 规则ID
    QString name;                  // 规则名称
    QString description;           // 规则描述
    SensitiveDataType targetType;  // 目标数据类型
    int baseScore;                 // 基础分数
    QVariantMap conditions;        // 评分条件
    QVariantMap weights;           // 权重配置
    bool enabled;                  // 是否启用
    QDateTime createdTime;         // 创建时间
    QDateTime modifiedTime;        // 修改时间
};
```

### 3. 合规标准模型 (ComplianceStandard)
```cpp
struct ComplianceStandard {
    QString id;                    // 标准ID (GDPR/CCPA/PIPL等)
    QString name;                  // 标准名称
    QString version;               // 版本
    QStringList requirements;      // 合规要求列表
    QVariantMap checkRules;        // 检查规则
    QVariantMap penalties;         // 违规处罚信息
    bool enabled;                  // 是否启用
};
```

### 4. 影响评估模型 (ImpactAssessment)
```cpp
struct ImpactAssessment {
    QString id;                    // 评估ID
    QString name;                  // 评估名称
    QString description;           // 评估描述
    QStringList affectedAssets;    // 受影响的资产
    QVariantMap riskFactors;       // 风险因素
    QVariantMap mitigationMeasures;// 缓解措施
    QString status;                // 状态 (Draft/InProgress/Completed)
    QDateTime createdTime;         // 创建时间
    QDateTime completedTime;       // 完成时间
    QString assessor;              // 评估人员
};
```

## 🔧 实现步骤详解

### 第一阶段：核心架构实现
1. **完善RiskAssessmentEngine.h头文件**
   - 定义所有数据结构
   - 声明主要接口方法
   - 设计信号槽机制

2. **实现RiskAssessmentEngine.cpp基础框架**
   - 构造函数和析构函数
   - 基础属性访问器
   - 初始化方法

### 第二阶段：数据管理模块
1. **AssetManager实现**
   - 资产CRUD操作
   - JSON序列化/反序列化
   - 资产搜索和过滤

2. **RuleManager实现**
   - 规则管理接口
   - 规则验证逻辑
   - 规则导入导出

3. **ComplianceStandardManager实现**
   - 标准定义管理
   - 标准更新机制
   - 多标准支持

### 第三阶段：计算引擎实现
1. **RiskScoreCalculator实现**
   - 多维度评分算法
   - 权重计算逻辑
   - 评分历史记录

2. **ComplianceChecker实现**
   - 合规规则执行引擎
   - 违规检测算法
   - 合规报告生成

3. **ImpactAssessmentEngine实现**
   - DPIA流程管理
   - 风险识别算法
   - 缓解措施建议

### 第四阶段：集成与优化
1. **与DataDiscoveryEngine集成**
   - 数据发现结果导入
   - 实时风险监控
   - 自动评估触发

2. **报告生成系统**
   - PDF报告生成
   - Excel数据导出
   - 图表可视化

3. **QML界面连接**
   - 注册C++类型到QML
   - 实现界面交互逻辑
   - 数据绑定和更新

## 🎨 QML界面集成方案

### 主要Q_INVOKABLE方法
```cpp
// 资产管理
Q_INVOKABLE QVariantList getAssets() const;
Q_INVOKABLE bool addAsset(const QVariantMap& asset);
Q_INVOKABLE bool updateAsset(const QString& id, const QVariantMap& asset);
Q_INVOKABLE bool removeAsset(const QString& id);

// 风险评估
Q_INVOKABLE void calculateRiskScore(const QString& assetId);
Q_INVOKABLE QVariantMap getRiskOverview() const;
Q_INVOKABLE QVariantList getRiskDistribution() const;

// 合规检查
Q_INVOKABLE void startComplianceCheck(const QString& standard);
Q_INVOKABLE QVariantMap getComplianceResult(const QString& standard) const;

// 影响评估
Q_INVOKABLE QString createImpactAssessment(const QVariantMap& assessment);
Q_INVOKABLE QVariantList getImpactAssessments() const;

// 报告生成
Q_INVOKABLE bool generateRiskReport(const QString& format, const QString& path);
```

### 信号设计
```cpp
signals:
    void riskScoreUpdated(const QString& assetId, int newScore);
    void complianceCheckCompleted(const QString& standard, const QVariantMap& result);
    void assessmentStatusChanged(const QString& assessmentId, const QString& status);
    void reportGenerated(const QString& reportPath);
    void errorOccurred(const QString& error);
```

## 📈 性能优化策略
1. **缓存机制**：评分结果缓存，避免重复计算
2. **异步处理**：大量数据处理使用工作线程
3. **增量更新**：只更新变化的数据
4. **内存管理**：合理使用智能指针和RAII

## 🧪 测试策略
1. **单元测试**：每个类的核心功能测试
2. **集成测试**：模块间交互测试
3. **性能测试**：大数据量下的性能验证
4. **UI测试**：QML界面交互测试

## 📝 开发注意事项
1. **线程安全**：多线程环境下的数据访问保护
2. **错误处理**：完善的异常处理机制
3. **日志记录**：详细的操作日志
4. **配置管理**：灵活的配置参数
5. **国际化**：支持多语言界面

## 🔄 与现有系统集成点
1. **DataDiscoveryEngine集成**
   - 复用敏感数据发现结果
   - 共享风险级别枚举
   - 统一的数据类型定义

2. **主程序集成**
   - 在main.cpp中注册新类型
   - 设置全局上下文属性
   - 配置QML模块导入

## 📋 实现优先级
1. **高优先级**：核心评分算法、基础数据管理
2. **中优先级**：合规检查、报告生成
3. **低优先级**：高级可视化、性能优化

## 🎯 当前进度
- [x] 创建RiskAssessmentEngine.h基础框架
- [x] 解决类型重定义问题
- [x] 集成DataDiscoveryEngine依赖
- [ ] 完善数据结构定义
- [ ] 实现基础构造函数
- [ ] 添加核心接口方法

---
*此文档将随着实现进度持续更新*