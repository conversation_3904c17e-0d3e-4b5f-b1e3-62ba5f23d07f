import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

ScrollView {
    id: root
    anchors.fill: parent
    contentWidth: availableWidth

    ColumnLayout {
        width: parent.width
        anchors.margins: 20
        spacing: 20

        // 页面标题
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")

            RowLayout {
                anchors.fill: parent
                anchors.margins: 8

                Text {
                    text: "🔄 系统集成"
                    font.pixelSize: 24
                    font.bold: true
                    color: Qt.color("#2c3e50")
                }

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "集成向导"
                    Material.background: Qt.color("#3498db")
                    Material.foreground: Qt.color("white")
                    onClicked:
                    // 打开集成向导
                    {}
                }
            }
        }

        // 数据导入区域
        GroupBox {
            Layout.fillWidth: true
            title: "数据导入"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 支持的格式
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 80
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 8

                        Text {
                            text: "支持的导入格式"
                            font.bold: true
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: "• Excel (.xlsx, .xls)"
                                color: Qt.color("#27ae60")
                            }
                            Text {
                                text: "• CSV (.csv)"
                                color: Qt.color("#27ae60")
                            }
                            Text {
                                text: "• JSON (.json)"
                                color: Qt.color("#27ae60")
                            }
                            Text {
                                text: "• XML (.xml)"
                                color: Qt.color("#27ae60")
                            }
                            Text {
                                text: "• 数据库 (SQL Server, MySQL)"
                                color: Qt.color("#27ae60")
                            }
                        }
                    }
                }

                // 导入配置
                RowLayout {
                    Layout.fillWidth: true

                    TextField {
                        Layout.fillWidth: true
                        placeholderText: "选择要导入的文件..."
                    }

                    Button {
                        text: "浏览文件"
                        onClicked:
                        // 浏览文件对话框
                        {}
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["自动检测", "Excel", "CSV", "JSON", "XML", "数据库"]
                        currentIndex: 0
                    }
                }

                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "导入选项:"
                        font.bold: true
                    }

                    CheckBox {
                        text: "跳过重复数据"
                        checked: true
                    }

                    CheckBox {
                        text: "验证数据格式"
                        checked: true
                    }

                    CheckBox {
                        text: "创建备份"
                        checked: true
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Button {
                        text: "开始导入"
                        Material.background: Qt.color("#27ae60")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 开始导入数据
                        {}
                    }
                }

                // 导入进度
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("white")
                    visible: false

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 8

                        Text {
                            text: "正在导入: customer_data.xlsx"
                            font.bold: true
                        }

                        ProgressBar {
                            Layout.fillWidth: true
                            value: 0.65
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: "已处理: 6,500 / 10,000 条记录"
                            }
                            Item {
                                Layout.fillWidth: true
                            }
                            Text {
                                text: "预计剩余时间: 2分钟"
                            }
                        }
                    }
                }

                // 导入历史
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(150, importHistoryView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: importHistoryView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "customer_data.xlsx"
                                records: "10,000"
                                date: "2024-01-15 14:30"
                                status: "成功"
                                errors: "0"
                            }
                            ListElement {
                                name: "employee_info.csv"
                                records: "1,250"
                                date: "2024-01-14 09:15"
                                status: "成功"
                                errors: "5"
                            }
                            ListElement {
                                name: "product_catalog.json"
                                records: "3,500"
                                date: "2024-01-13 16:45"
                                status: "失败"
                                errors: "125"
                            }
                            ListElement {
                                name: "user_permissions.xml"
                                records: "890"
                                date: "2024-01-12 11:20"
                                status: "成功"
                                errors: "0"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: "文件名"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "记录数"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "导入时间"
                                    font.bold: true
                                    Layout.preferredWidth: 140
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 60
                                }
                                Text {
                                    text: "错误"
                                    font.bold: true
                                    Layout.preferredWidth: 60
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: importHistoryDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 35
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: importHistoryDelegateItem.model.name
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: importHistoryDelegateItem.model.records
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: importHistoryDelegateItem.model.date
                                    Layout.preferredWidth: 140
                                    font.family: "Consolas"
                                }
                                Text {
                                    text: importHistoryDelegateItem.model.status
                                    Layout.preferredWidth: 60
                                    color: importHistoryDelegateItem.model.status === "成功" ? Qt.color("#27ae60") : Qt.color("#e74c3c")
                                }
                                Text {
                                    text: importHistoryDelegateItem.model.errors
                                    Layout.preferredWidth: 60
                                    color: importHistoryDelegateItem.model.errors === "0" ? Qt.color("#27ae60") : Qt.color("#e74c3c")
                                }
                            }
                        }
                    }
                }
            }
        }

        // 数据导出区域
        GroupBox {
            Layout.fillWidth: true
            title: "数据导出"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 导出配置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "数据源:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 200
                        model: ["所有数据", "客户信息", "员工档案", "产品目录", "订单记录", "日志数据"]
                        currentIndex: 0
                    }

                    Text {
                        text: "导出格式:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 120
                        model: ["Excel", "CSV", "JSON", "XML", "PDF"]
                        currentIndex: 0
                    }

                    Button {
                        text: "高级选项"
                        onClicked:
                        // 打开高级导出选项
                        {}
                    }
                }

                // 导出选项
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(120, exportOptions.implicitHeight + 32)
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        id: exportOptions
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 12

                        Text {
                            text: "导出选项"
                            font.bold: true
                        }

                        GridLayout {
                            Layout.fillWidth: true
                            columns: 3

                            CheckBox {
                                text: "包含标题行"
                                checked: true
                            }
                            CheckBox {
                                text: "数据脱敏"
                                checked: false
                            }
                            CheckBox {
                                text: "压缩文件"
                                checked: false
                            }
                            CheckBox {
                                text: "分页导出"
                                checked: false
                            }
                            CheckBox {
                                text: "添加时间戳"
                                checked: true
                            }
                            CheckBox {
                                text: "数字签名"
                                checked: false
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: "时间范围:"
                                font.bold: true
                            }
                            TextField {
                                Layout.preferredWidth: 120
                                text: "2024-01-01"
                                placeholderText: "开始日期"
                            }
                            Text {
                                text: "至"
                            }
                            TextField {
                                Layout.preferredWidth: 120
                                text: "2024-01-15"
                                placeholderText: "结束日期"
                            }

                            Item {
                                Layout.fillWidth: true
                            }

                            Button {
                                text: "开始导出"
                                Material.background: "#f39c12"
                                Material.foreground: Qt.color("white")
                                onClicked:
                                // 开始导出数据
                                {}
                            }
                        }
                    }
                }

                // 导出任务列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(150, exportTaskView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: exportTaskView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "客户信息导出"
                                format: "Excel"
                                progress: 100
                                status: "已完成"
                                size: "2.3 MB"
                            }
                            ListElement {
                                name: "订单记录导出"
                                format: "CSV"
                                progress: 45
                                status: "导出中"
                                size: "1.2 MB"
                            }
                            ListElement {
                                name: "日志数据导出"
                                format: "JSON"
                                progress: 0
                                status: "队列中"
                                size: "-"
                            }
                            ListElement {
                                name: "产品目录导出"
                                format: "PDF"
                                progress: 100
                                status: "已完成"
                                size: "856 KB"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 45
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: "任务名称"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "格式"
                                    font.bold: true
                                    Layout.preferredWidth: 60
                                }
                                Text {
                                    text: "进度"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "大小"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "操作"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: exportDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0

                                Text {
                                    text: exportDelegateItem.model.name
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: exportDelegateItem.model.format
                                    Layout.preferredWidth: 60
                                }
                                ProgressBar {
                                    Layout.preferredWidth: 100
                                    value: exportDelegateItem.model.progress / 100
                                }
                                Text {
                                    text: exportDelegateItem.model.status
                                    Layout.preferredWidth: 80
                                    color: exportDelegateItem.model.status === "已完成" ? Qt.color("#27ae60") : (exportDelegateItem.model.status === "导出中" ? Qt.color("#f39c12") : Qt.color("#95a5a6"))
                                }
                                Text {
                                    text: exportDelegateItem.model.size
                                    Layout.preferredWidth: 80
                                }
                                RowLayout {
                                    Layout.preferredWidth: 100

                                    Button {
                                        text: exportDelegateItem.model.status === "已完成" ? "下载" : "取消"
                                        flat: true
                                        Material.foreground: exportDelegateItem.model.status === "已完成" ? "#27ae60" : "#e74c3c"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 数据库连接区域
        GroupBox {
            Layout.fillWidth: true
            title: "数据库连接"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 连接配置
                GridLayout {
                    Layout.fillWidth: true
                    columns: 4

                    Text {
                        text: "数据库类型:"
                        font.bold: true
                    }
                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["SQL Server", "MySQL", "PostgreSQL", "Oracle", "SQLite"]
                        currentIndex: 0
                    }

                    Text {
                        text: "服务器地址:"
                        font.bold: true
                    }
                    TextField {
                        Layout.preferredWidth: 200
                        placeholderText: "localhost"
                    }

                    Text {
                        text: "端口:"
                        font.bold: true
                    }
                    TextField {
                        Layout.preferredWidth: 100
                        placeholderText: "1433"
                    }

                    Text {
                        text: "数据库名:"
                        font.bold: true
                    }
                    TextField {
                        Layout.preferredWidth: 150
                        placeholderText: "DataProtection"
                    }

                    Text {
                        text: "用户名:"
                        font.bold: true
                    }
                    TextField {
                        Layout.preferredWidth: 150
                        placeholderText: "sa"
                    }

                    Text {
                        text: "密码:"
                        font.bold: true
                    }
                    TextField {
                        Layout.preferredWidth: 150
                        echoMode: TextInput.Password
                        placeholderText: "password"
                    }
                }

                RowLayout {
                    Layout.fillWidth: true

                    Button {
                        text: "测试连接"
                        Material.background: "#3498db"
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 测试数据库连接
                        {}
                    }

                    Button {
                        text: "保存配置"
                        Material.background: "#27ae60"
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 保存数据库配置
                        {}
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Rectangle {
                        Layout.preferredWidth: 12
                        Layout.preferredHeight: 12
                        radius: 6
                        color: Qt.color("#27ae60")
                    }

                    Text {
                        text: "连接正常"
                        color: Qt.color("#27ae60")
                        font.bold: true
                    }
                }

                // 已保存的连接
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(150, connectionListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: connectionListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "主数据库"
                                type: "SQL Server"
                                server: "localhost:1433"
                                database: "DataProtection"
                                status: "已连接"
                            }
                            ListElement {
                                name: "备份数据库"
                                type: "MySQL"
                                server: "backup.local:3306"
                                database: "backup_db"
                                status: "未连接"
                            }
                            ListElement {
                                name: "测试数据库"
                                type: "SQLite"
                                server: "local"
                                database: "test.db"
                                status: "已连接"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 25
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 6

                                Text {
                                    text: "连接名称"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "类型"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "服务器"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "数据库"
                                    font.bold: true
                                    Layout.preferredWidth: 140
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "操   作"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: connectionDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0

                                Text {
                                    text: connectionDelegateItem.model.name
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: connectionDelegateItem.model.type
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: connectionDelegateItem.model.server
                                    Layout.fillWidth: true
                                    font.family: "Consolas"
                                }
                                Text {
                                    text: connectionDelegateItem.model.database
                                    Layout.preferredWidth: 120
                                }
                                Text {
                                    text: connectionDelegateItem.model.status
                                    Layout.preferredWidth: 80
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                    color: connectionDelegateItem.model.status === "已连接" ? Qt.color("#27ae60") : Qt.color("#e74c3c")
                                }
                                RowLayout {
                                    Layout.preferredWidth: 100
                                    spacing: 4

                                    Button {
                                        text: "连接"
                                        flat: true
                                        Material.foreground: Qt.color("#3498db")
                                    }

                                    Button {
                                        text: "删除"
                                        flat: true
                                        Material.foreground: Qt.color("#e74c3c")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Item {
            Layout.preferredHeight: 20
        }
    }
}
