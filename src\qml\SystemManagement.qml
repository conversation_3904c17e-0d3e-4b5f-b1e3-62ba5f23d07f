import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

ScrollView {
    id: root
    anchors.fill: parent
    contentWidth: availableWidth

    ColumnLayout {
        width: parent.width
        anchors.margins: 20
        spacing: 20

        // 页面标题
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")

            RowLayout {
                anchors.fill: parent
                anchors.margins: 8

                Text {
                    text: "⚙️ 系统管理"
                    font.pixelSize: 24
                    font.bold: true
                    color: Qt.color("#2c3e50")
                }

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "系统诊断"
                    Material.background: Qt.color("#3498db")
                    Material.foreground: Qt.color("white")
                    onClicked:
                    // 系统诊断功能
                    {}
                }
            }
        }

        // 直观控制面板区域
        GroupBox {
            Layout.fillWidth: true
            title: "直观控制面板"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 系统状态概览
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 150
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    GridLayout {
                        anchors.fill: parent
                        anchors.margins: 16
                        columns: 4
                        rowSpacing: 16
                        columnSpacing: 20

                        // CPU使用率
                        ColumnLayout {
                            spacing: 8

                            Text {
                                text: "CPU使用率"
                                font.bold: true
                                horizontalAlignment: Text.AlignHCenter
                                Layout.alignment: Qt.AlignHCenter
                            }

                            Rectangle {
                                Layout.preferredWidth: 80
                                Layout.preferredHeight: 80
                                radius: 40
                                color: Qt.color("#3498db")
                                Layout.alignment: Qt.AlignHCenter

                                Text {
                                    anchors.centerIn: parent
                                    text: "23%"
                                    font.pixelSize: 18
                                    font.bold: true
                                    color: Qt.color("white")
                                }
                            }

                            Text {
                                text: "正常"
                                color: Qt.color("#27ae60")
                                font.bold: true
                                Layout.alignment: Qt.AlignHCenter
                            }
                        }

                        // 内存使用率
                        ColumnLayout {
                            spacing: 8

                            Text {
                                text: "内存使用率"
                                font.bold: true
                                horizontalAlignment: Text.AlignHCenter
                                Layout.alignment: Qt.AlignHCenter
                            }

                            Rectangle {
                                Layout.preferredWidth: 80
                                Layout.preferredHeight: 80
                                radius: 40
                                color: Qt.color("#f39c12")
                                Layout.alignment: Qt.AlignHCenter

                                Text {
                                    anchors.centerIn: parent
                                    text: "67%"
                                    font.pixelSize: 18
                                    font.bold: true
                                    color: Qt.color("white")
                                }
                            }

                            Text {
                                text: "中等"
                                color: Qt.color("#f39c12")
                                font.bold: true
                                Layout.alignment: Qt.AlignHCenter
                            }
                        }

                        // 磁盘使用率
                        ColumnLayout {
                            spacing: 8

                            Text {
                                text: "磁盘使用率"
                                font.bold: true
                                horizontalAlignment: Text.AlignHCenter
                                Layout.alignment: Qt.AlignHCenter
                            }

                            Rectangle {
                                Layout.preferredWidth: 80
                                Layout.preferredHeight: 80
                                radius: 40
                                color: Qt.color("#27ae60")
                                Layout.alignment: Qt.AlignHCenter

                                Text {
                                    anchors.centerIn: parent
                                    text: "45%"
                                    font.pixelSize: 18
                                    font.bold: true
                                    color: Qt.color("white")
                                }
                            }

                            Text {
                                text: "良好"
                                color: Qt.color("#27ae60")
                                font.bold: true
                                Layout.alignment: Qt.AlignHCenter
                            }
                        }

                        // 网络状态
                        ColumnLayout {
                            spacing: 8

                            Text {
                                text: "网络状态"
                                font.bold: true
                                horizontalAlignment: Text.AlignHCenter
                                Layout.alignment: Qt.AlignHCenter
                            }

                            Rectangle {
                                Layout.preferredWidth: 80
                                Layout.preferredHeight: 80
                                radius: 40
                                color: Qt.color("#27ae60")
                                Layout.alignment: Qt.AlignHCenter

                                Text {
                                    anchors.centerIn: parent
                                    text: "✓"
                                    font.pixelSize: 32
                                    font.bold: true
                                    color: Qt.color("white")
                                }
                            }

                            Text {
                                text: "连接正常"
                                color: Qt.color("#27ae60")
                                font.bold: true
                                Layout.alignment: Qt.AlignHCenter
                            }
                        }
                    }
                }

                // 快速操作按钮
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 12

                    Button {
                        text: "重启服务"
                        Material.background: Qt.color("#e74c3c")
                        Material.foreground: "white"
                        onClicked:
                        // 重启服务
                        {}
                    }

                    Button {
                        text: "清理缓存"
                        Material.background: Qt.color("#f39c12")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 清理缓存
                        {}
                    }

                    Button {
                        text: "优化性能"
                        Material.background: Qt.color("#9b59b6")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 优化性能
                        {}
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "运行时间: 3天 15小时 42分钟"
                        color: Qt.color("#7f8c8d")
                        font.bold: true
                    }
                }
            }
        }

        // 资源优化区域
        GroupBox {
            Layout.fillWidth: true
            title: "资源优化"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 优化设置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "优化级别:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["节能模式", "平衡模式", "性能模式", "自定义"]
                        currentIndex: 1
                    }

                    CheckBox {
                        text: "自动优化"
                        checked: true
                    }

                    CheckBox {
                        text: "后台运行"
                        checked: true
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Button {
                        text: "立即优化"
                        Material.background: Qt.color("#27ae60")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 立即优化
                        {}
                    }
                }

                // 优化项目
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(200, optimizationListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: optimizationListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "内存清理"
                                description: "清理未使用的内存"
                                enabled: true
                                status: "已启用"
                                impact: "中"
                            }
                            ListElement {
                                name: "磁盘碎片整理"
                                description: "优化磁盘存储"
                                enabled: false
                                status: "已禁用"
                                impact: "高"
                            }
                            ListElement {
                                name: "启动项优化"
                                description: "减少启动时间"
                                enabled: true
                                status: "已启用"
                                impact: "低"
                            }
                            ListElement {
                                name: "网络连接优化"
                                description: "优化网络性能"
                                enabled: true
                                status: "已启用"
                                impact: "中"
                            }
                            ListElement {
                                name: "数据库索引优化"
                                description: "提升查询性能"
                                enabled: true
                                status: "已启用"
                                impact: "高"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: "优化项目"
                                    font.bold: true
                                    Layout.preferredWidth: 120
                                }
                                Text {
                                    text: "描述"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 60
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "影响"
                                    font.bold: true
                                    Layout.preferredWidth: 40
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "启用"
                                    font.bold: true
                                    Layout.preferredWidth: 40
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: optimizationDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0

                                Text {
                                    text: optimizationDelegateItem.model.name
                                    Layout.preferredWidth: 120
                                    font.bold: true
                                }
                                Text {
                                    text: optimizationDelegateItem.model.description
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: optimizationDelegateItem.model.status
                                    Layout.preferredWidth: 60
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                    color: optimizationDelegateItem.model.enabled ? Qt.color("#27ae60") : Qt.color("#95a5a6")
                                }
                                Text {
                                    text: optimizationDelegateItem.model.impact
                                    Layout.preferredWidth: 40
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                    color: optimizationDelegateItem.model.impact === "高" ? Qt.color("#e74c3c") : (optimizationDelegateItem.model.impact === "中" ? Qt.color("#f39c12") : Qt.color("#27ae60"))
                                }
                                CheckBox {
                                    Layout.preferredWidth: 40
                                    checked: optimizationDelegateItem.model.enabled
                                }
                            }
                        }
                    }
                }
            }
        }

        // 自动更新区域
        GroupBox {
            Layout.fillWidth: true
            title: "自动更新"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 更新设置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "更新频率:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["每日检查", "每周检查", "每月检查", "手动检查"]
                        currentIndex: 1
                    }

                    CheckBox {
                        text: "自动下载"
                        checked: true
                    }

                    CheckBox {
                        text: "自动安装"
                        checked: false
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Button {
                        text: "检查更新"
                        Material.background: Qt.color("#3498db")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 检查更新
                        {}
                    }
                }

                // 更新状态
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 8

                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: "当前版本: v1.0.0"
                                font.bold: true
                            }

                            Item {
                                Layout.fillWidth: true
                            }

                            Rectangle {
                                Layout.preferredWidth: 12
                                Layout.preferredHeight: 12
                                radius: 6
                                color: Qt.color("#27ae60")
                            }

                            Text {
                                text: "已是最新版本"
                                color: Qt.color("#27ae60")
                                font.bold: true
                            }
                        }

                        Text {
                            text: "最后检查时间: 2024-01-15 14:30:25"
                            color: Qt.color("#7f8c8d")
                        }

                        Text {
                            text: "下次检查时间: 2024-01-22 14:30:25"
                            color: Qt.color("#7f8c8d")
                        }
                    }
                }

                // 更新历史
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(120, updateHistoryView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: updateHistoryView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                version: "v1.0.0"
                                date: "2024-01-15"
                                type: "正式版"
                                description: "初始发布版本"
                            }
                            ListElement {
                                version: "v0.9.5"
                                date: "2024-01-10"
                                type: "测试版"
                                description: "修复已知问题"
                            }
                            ListElement {
                                version: "v0.9.0"
                                date: "2024-01-05"
                                type: "测试版"
                                description: "新增系统管理功能"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 25
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 6

                                Text {
                                    text: "版本"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "日期"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "类型"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "描述"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: versionDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 30
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 6

                                Text {
                                    text: versionDelegateItem.model.version
                                    Layout.preferredWidth: 80
                                    font.bold: true
                                }
                                Text {
                                    text: versionDelegateItem.model.date
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: versionDelegateItem.model.type
                                    Layout.preferredWidth: 80
                                    color: versionDelegateItem.model.type === "正式版" ? Qt.color("#27ae60") : Qt.color("#f39c12")
                                }
                                Text {
                                    text: versionDelegateItem.model.description
                                    Layout.fillWidth: true
                                }
                            }
                        }
                    }
                }
            }
        }

        // 备份与恢复区域
        GroupBox {
            Layout.fillWidth: true
            title: "备份与恢复"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 备份设置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "备份策略:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["每日备份", "每周备份", "每月备份", "手动备份"]
                        currentIndex: 0
                    }

                    Text {
                        text: "保留数量:"
                        font.bold: true
                    }

                    SpinBox {
                        Layout.preferredWidth: 100
                        from: 1
                        to: 30
                        value: 7
                    }

                    Button {
                        text: "立即备份"
                        Material.background: Qt.color("#27ae60")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 立即备份
                        {}
                    }
                }

                // 备份选项
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "备份内容:"
                        font.bold: true
                    }

                    CheckBox {
                        text: "配置文件"
                        checked: true
                    }

                    CheckBox {
                        text: "数据库"
                        checked: true
                    }

                    CheckBox {
                        text: "日志文件"
                        checked: false
                    }

                    CheckBox {
                        text: "用户数据"
                        checked: true
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    TextField {
                        Layout.preferredWidth: 200
                        text: "C:\\Backup\\"
                        placeholderText: "备份路径"
                    }

                    Button {
                        text: "浏览"
                        onClicked:
                        // 选择备份路径
                        {}
                    }
                }

                // 备份列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(150, backupListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: backupListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "backup_20240115_143025.zip"
                                date: "2024-01-15 14:30"
                                size: "125.6 MB"
                                type: "完整备份"
                                status: "成功"
                            }
                            ListElement {
                                name: "backup_20240114_143025.zip"
                                date: "2024-01-14 14:30"
                                size: "123.2 MB"
                                type: "完整备份"
                                status: "成功"
                            }
                            ListElement {
                                name: "backup_20240113_143025.zip"
                                date: "2024-01-13 14:30"
                                size: "121.8 MB"
                                type: "完整备份"
                                status: "成功"
                            }
                            ListElement {
                                name: "backup_20240112_143025.zip"
                                date: "2024-01-12 14:30"
                                size: "120.4 MB"
                                type: "完整备份"
                                status: "失败"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: "备份文件"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "备份时间"
                                    font.bold: true
                                    Layout.preferredWidth: 130
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "大小"
                                    font.bold: true
                                    Layout.preferredWidth: 90
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "类型"
                                    font.bold: true
                                    Layout.preferredWidth: 60
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "操   作"
                                    font.bold: true
                                    Layout.preferredWidth: 120
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: backupDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0

                                Text {
                                    text: backupDelegateItem.model.name
                                    Layout.fillWidth: true
                                    font.family: "Consolas"
                                }
                                Text {
                                    text: backupDelegateItem.model.date
                                    Layout.preferredWidth: 140
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                    font.family: "Consolas"
                                }
                                Text {
                                    text: backupDelegateItem.model.size
                                    Layout.preferredWidth: 80
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: backupDelegateItem.model.type
                                    Layout.preferredWidth: 80
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: backupDelegateItem.model.status
                                    Layout.preferredWidth: 60
                                    //水平居中
                                    horizontalAlignment: Text.AlignHCenter
                                    color: backupDelegateItem.model.status === "成功" ? Qt.color("#27ae60") : Qt.color("#e74c3c")
                                }
                                RowLayout {
                                    Layout.preferredWidth: 100
                                    spacing: 4

                                    Button {
                                        text: "恢复"
                                        flat: true
                                        Material.foreground: Qt.color("#3498db")
                                        enabled: backupDelegateItem.model.status === "成功"
                                    }

                                    Button {
                                        text: "删除"
                                        flat: true
                                        Material.foreground: Qt.color("#e74c3c")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Item {
            Layout.preferredHeight: 20
        }
    }
}
