import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

ScrollView {
    id: root
    anchors.fill: parent
    contentWidth: availableWidth

    ColumnLayout {
        width: parent.width
        anchors.margins: 20
        spacing: 20

        // 页面标题
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")

            RowLayout {
                anchors.fill: parent
                anchors.margins: 8

                Text {
                    text: "🔒 数据保护与控制"
                    font.pixelSize: 24
                    font.bold: true
                    color: Qt.color("#2c3e50")
                }

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "保护策略概览"
                    Material.background: Qt.color("#27ae60")
                    Material.foreground: Qt.color("white")
                    onClicked:
                    // 保护策略概览
                    {}
                }
            }
        }

        // 本地数据加密区域
        GroupBox {
            Layout.fillWidth: true
            title: "本地数据加密"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 加密设置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "加密算法:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["AES-256", "AES-128", "ChaCha20", "3DES"]
                        currentIndex: 0
                    }

                    Text {
                        text: "密钥管理:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["本地密钥库", "硬件安全模块", "云密钥管理"]
                        currentIndex: 0
                    }

                    Button {
                        text: "生成新密钥"
                        Material.background: Qt.color("#3498db")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 生成新密钥
                        {}
                    }
                }

                // 文件加密操作
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(250, encryptionLayout.height + 32)
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        id: encryptionLayout
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 12

                        Text {
                            text: "文件加密操作"
                            font.bold: true
                            font.pixelSize: 16
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            TextField {
                                Layout.fillWidth: true
                                placeholderText: "选择要加密的文件或文件夹..."
                            }

                            Button {
                                text: "浏览"
                                onClicked:
                                // 浏览文件
                                {}
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            CheckBox {
                                text: "递归加密子目录"
                                checked: true
                            }

                            CheckBox {
                                text: "保留原文件"
                                checked: false
                            }

                            CheckBox {
                                text: "压缩后加密"
                                checked: true
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Button {
                                text: "开始加密"
                                Material.background: Qt.color("#27ae60")
                                Material.foreground: Qt.color("white")
                                onClicked:
                                // 开始加密
                                {}
                            }

                            Button {
                                text: "批量解密"
                                Material.background: Qt.color("#e74c3c")
                                Material.foreground: Qt.color("white")
                                onClicked:
                                // 批量解密
                                {}
                            }

                            Item {
                                Layout.fillWidth: true
                            }

                            Text {
                                text: "已加密文件: 1,234 个"
                                color: Qt.color("#27ae60")
                                font.bold: true
                            }
                        }

                        ProgressBar {
                            Layout.fillWidth: true
                            value: 0.0
                            visible: false
                        }
                    }
                }

                // 加密文件列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(200, encryptedListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: encryptedListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "客户数据.xlsx.enc"
                                size: "2.5 MB"
                                date: "2024-01-15"
                                status: "已加密"
                            }
                            ListElement {
                                name: "财务报表.pdf.enc"
                                size: "1.8 MB"
                                date: "2024-01-14"
                                status: "已加密"
                            }
                            ListElement {
                                name: "员工信息.db.enc"
                                size: "15.2 MB"
                                date: "2024-01-13"
                                status: "已加密"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 35
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 12
                                spacing: 8

                                Text {
                                    text: "文件名"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "大小"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "加密日期"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "操作"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: encryptedFileDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0

                                Text {
                                    text: encryptedFileDelegateItem.model.name
                                    Layout.fillWidth: true
                                    font.pixelSize: 14
                                }
                                Text {
                                    text: encryptedFileDelegateItem.model.size
                                    Layout.preferredWidth: 80
                                    font.pixelSize: 14
                                }
                                Text {
                                    text: encryptedFileDelegateItem.model.date
                                    Layout.preferredWidth: 100
                                    font.pixelSize: 14
                                }
                                Text {
                                    text: encryptedFileDelegateItem.model.status
                                    Layout.preferredWidth: 80
                                    font.pixelSize: 14
                                    color: Qt.color("#27ae60")
                                }

                                RowLayout {
                                    Layout.preferredWidth: 100

                                    Button {
                                        text: "解密"
                                        flat: true
                                        Material.foreground: Qt.color("#e74c3c")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 数据脱敏工具区域
        GroupBox {
            Layout.fillWidth: true
            title: "数据脱敏工具"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 脱敏规则设置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "脱敏类型:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["掩码脱敏", "替换脱敏", "加密脱敏", "哈希脱敏"]
                        currentIndex: 0
                    }

                    Text {
                        text: "数据类型:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["身份证号", "手机号", "邮箱", "银行卡号", "姓名", "地址"]
                        currentIndex: 0
                    }

                    Button {
                        text: "自定义规则"
                        onClicked:
                        // 自定义脱敏规则
                        {}
                    }
                }

                // 脱敏预览
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(150, desensitizationPreview.height + 32)
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        id: desensitizationPreview
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 8

                        Text {
                            text: "脱敏预览"
                            font.bold: true
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: "原始数据:"
                                Layout.preferredWidth: 80
                                font.bold: true
                            }

                            Text {
                                text: "张三 | 13812345678 | 110101199001011234"
                                Layout.fillWidth: true
                                color: Qt.color("#e74c3c")
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: "脱敏后:"
                                Layout.preferredWidth: 80
                                font.bold: true
                            }

                            Text {
                                text: "张** | 138****5678 | 110101********1234"
                                Layout.fillWidth: true
                                color: Qt.color("#27ae60")
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Button {
                                text: "应用脱敏"
                                Material.background: Qt.color("#f39c12")
                                Material.foreground: Qt.color("white")
                                onClicked:
                                // 应用脱敏
                                {}
                            }

                            Button {
                                text: "批量脱敏"
                                Material.background: Qt.color("#9b59b6")
                                Material.foreground: Qt.color("white")
                                onClicked:
                                // 批量脱敏
                                {}
                            }

                            Item {
                                Layout.fillWidth: true
                            }
                        }
                    }
                }

                // 脱敏任务列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(120, desensitizationListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: desensitizationListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "客户信息脱敏"
                                type: "掩码脱敏"
                                progress: 100
                                status: "已完成"
                            }
                            ListElement {
                                name: "员工档案脱敏"
                                type: "替换脱敏"
                                progress: 75
                                status: "进行中"
                            }
                            ListElement {
                                name: "订单数据脱敏"
                                type: "加密脱敏"
                                progress: 0
                                status: "等待中"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 25
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 6

                                Text {
                                    text: "任务名称"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "脱敏类型"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "进度"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: desensitizationTaskDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 30
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 6

                                Text {
                                    text: desensitizationTaskDelegateItem.model.name
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: desensitizationTaskDelegateItem.model.type
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: desensitizationTaskDelegateItem.model.progress + "%"
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: desensitizationTaskDelegateItem.model.status
                                    Layout.preferredWidth: 80
                                    color: desensitizationTaskDelegateItem.model.status === "已完成" ? Qt.color("#27ae60") : (desensitizationTaskDelegateItem.model.status === "进行中" ? Qt.color("#f39c12") : Qt.color("#95a5a6"))
                                }
                            }
                        }
                    }
                }
            }
        }

        // 数据留存管理区域
        GroupBox {
            Layout.fillWidth: true
            title: "数据留存管理"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 留存策略设置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "默认留存期:"
                        font.bold: true
                    }

                    SpinBox {
                        Layout.preferredWidth: 100
                        from: 1
                        to: 999
                        value: 7
                    }

                    ComboBox {
                        Layout.preferredWidth: 100
                        model: ["年", "月", "天"]
                        currentIndex: 0
                    }

                    Text {
                        text: "清理策略:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["自动删除", "移动到归档", "标记过期", "通知管理员"]
                        currentIndex: 0
                    }

                    Button {
                        text: "立即清理"
                        Material.background: Qt.color("#e74c3c")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 立即清理
                        {}
                    }
                }

                // 留存统计
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    GridLayout {
                        anchors.fill: parent
                        anchors.margins: 16
                        columns: 4

                        Text {
                            text: "总数据量"
                            font.bold: true
                        }
                        Text {
                            text: "125.6 GB"
                            color: Qt.color("#3498db")
                        }
                        Text {
                            text: "即将过期"
                            font.bold: true
                        }
                        Text {
                            text: "2.3 GB"
                            color: Qt.color("#f39c12")
                        }
                        Text {
                            text: "已过期"
                            font.bold: true
                        }
                        Text {
                            text: "856 MB"
                            color: Qt.color("#e74c3c")
                        }
                        Text {
                            text: "已清理"
                            font.bold: true
                        }
                        Text {
                            text: "45.2 GB"
                            color: Qt.color("#27ae60")
                        }
                    }
                }

                // 留存规则列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(150, retentionListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: retentionListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "客户数据"
                                period: "7年"
                                action: "自动删除"
                                nextClean: "2031-01-15"
                            }
                            ListElement {
                                name: "日志文件"
                                period: "1年"
                                action: "移动到归档"
                                nextClean: "2025-01-15"
                            }
                            ListElement {
                                name: "临时文件"
                                period: "30天"
                                action: "自动删除"
                                nextClean: "2024-02-14"
                            }
                            ListElement {
                                name: "备份文件"
                                period: "3年"
                                action: "标记过期"
                                nextClean: "2027-01-15"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: "数据类型"
                                    font.bold: true
                                    Layout.fillWidth: true
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "留存期"
                                    font.bold: true
                                    Layout.preferredWidth: 90
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "清理动作"
                                    font.bold: true
                                    Layout.preferredWidth: 110
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "下次清理"
                                    font.bold: true
                                    Layout.preferredWidth: 110
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "操作"
                                    font.bold: true
                                    Layout.preferredWidth: 60
                                    horizontalAlignment: Text.AlignHCenter
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: retentionRulesDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0

                                Text {
                                    text: retentionRulesDelegateItem.model.name
                                    Layout.fillWidth: true
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    font.pixelSize: 14
                                }
                                Text {
                                    text: retentionRulesDelegateItem.model.period
                                    Layout.preferredWidth: 90
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    font.pixelSize: 14
                                }
                                Text {
                                    text: retentionRulesDelegateItem.model.action
                                    Layout.preferredWidth: 110
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    font.pixelSize: 14
                                }
                                Text {
                                    text: retentionRulesDelegateItem.model.nextClean
                                    Layout.preferredWidth: 110
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    font.pixelSize: 14
                                }
                                
                                RowLayout {
                                    Layout.preferredWidth: 60

                                    Button {
                                        text: "编辑"
                                        flat: true
                                        Material.foreground: Qt.color("#3498db")
                                        Layout.alignment: Qt.AlignHCenter
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Item {
            Layout.preferredHeight: 20
        }
    }
}
