import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

ScrollView {
    id: root
    anchors.fill: parent
    contentWidth: availableWidth

    ColumnLayout {
        width: parent.width
        anchors.margins: 20
        spacing: 20

        // 页面标题
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")

            RowLayout {
                anchors.fill: parent
                anchors.margins: 8

                Text {
                    text: "📋 合规与治理"
                    font.pixelSize: 24
                    font.bold: true
                    color: Qt.color("#2c3e50")
                }

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "合规性报告"
                    Material.background: Qt.color("#9b59b6")
                    Material.foreground: Qt.color("white")
                    onClicked:
                    // 生成合规性报告
                    {}
                }
            }
        }

        // 政策模板区域
        GroupBox {
            Layout.fillWidth: true
            title: "政策模板"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                RowLayout {
                    Layout.fillWidth: true

                    ComboBox {
                        Layout.preferredWidth: 200
                        model: ["所有模板", "隐私政策", "数据处理协议", "员工手册", "供应商协议"]
                        currentIndex: 0
                    }

                    Button {
                        text: "新建模板"
                        Material.background: Qt.color("#3498db")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 新建政策模板
                        {}
                    }

                    Button {
                        text: "导入模板"
                        onClicked:
                        // 导入政策模板
                        {}
                    }

                    Button {
                        text: "模板库"
                        onClicked:
                        // 打开模板库
                        {}
                    }
                }

                // 政策模板列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(200, templateListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: templateListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "GDPR隐私政策模板"
                                type: "隐私政策"
                                version: "v2.1"
                                status: "已发布"
                                lastUpdate: "2024-01-10"
                            }
                            ListElement {
                                name: "员工数据处理协议"
                                type: "数据处理协议"
                                version: "v1.3"
                                status: "草稿"
                                lastUpdate: "2024-01-08"
                            }
                            ListElement {
                                name: "第三方数据共享协议"
                                type: "供应商协议"
                                version: "v1.0"
                                status: "已发布"
                                lastUpdate: "2023-12-15"
                            }
                            ListElement {
                                name: "数据安全管理制度"
                                type: "员工手册"
                                version: "v3.2"
                                status: "已发布"
                                lastUpdate: "2023-11-20"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: "模板名称"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "类型"
                                    font.bold: true
                                    Layout.preferredWidth: 120
                                    horizontalAlignment: Text.AlignLeft
                                }
                                Text {
                                    text: "版本"
                                    font.bold: true
                                    Layout.preferredWidth: 60
                                    horizontalAlignment: Text.AlignLeft
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                    horizontalAlignment: Text.AlignLeft
                                }
                                Text {
                                    text: "更新日期"
                                    font.bold: true
                                    Layout.preferredWidth: 120
                                    horizontalAlignment: Text.AlignLeft
                                }
                                Text {
                                    text: "操作"
                                    font.bold: true
                                    Layout.preferredWidth: 180
                                    horizontalAlignment: Text.AlignLeft
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: policyDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0

                                Text {
                                    text: policyDelegateItem.model.name
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: policyDelegateItem.model.type
                                    Layout.preferredWidth: 120
                                }
                                Text {
                                    text: policyDelegateItem.model.version
                                    Layout.preferredWidth: 60
                                }
                                Text {
                                    text: policyDelegateItem.model.status
                                    Layout.preferredWidth: 80
                                    color: policyDelegateItem.model.status === "已发布" ? Qt.color("#27ae60") : Qt.color("#f39c12")
                                }
                                Text {
                                    text: policyDelegateItem.model.lastUpdate
                                    Layout.preferredWidth: 100
                                }
                                RowLayout {
                                    Layout.preferredWidth: 150
                                    spacing: 4

                                    Button {
                                        text: "编辑"
                                        flat: true
                                        Material.foreground: Qt.color("#3498db")
                                    }

                                    Button {
                                        text: "预览"
                                        flat: true
                                        Material.foreground: Qt.color("#3498db")
                                    }

                                    Button {
                                        text: "发布"
                                        flat: true
                                        Material.foreground: Qt.color("#27ae60")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 同意记录区域
        GroupBox {
            Layout.fillWidth: true
            title: "同意记录"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 同意统计
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    GridLayout {
                        anchors.fill: parent
                        anchors.margins: 16
                        columns: 4

                        Text {
                            text: "总同意记录"
                            font.bold: true
                        }
                        Text {
                            text: "15,678"
                            color: Qt.color("#3498db")
                        }
                        Text {
                            text: "有效同意"
                            font.bold: true
                        }
                        Text {
                            text: "14,523"
                            color: Qt.color("#27ae60")
                        }
                        Text {
                            text: "已撤销"
                            font.bold: true
                        }
                        Text {
                            text: "892"
                            color: Qt.color("#e74c3c")
                        }
                        Text {
                            text: "即将过期"
                            font.bold: true
                        }
                        Text {
                            text: "263"
                            color: Qt.color("#f39c12")
                        }
                    }
                }

                RowLayout {
                    Layout.fillWidth: true

                    TextField {
                        Layout.fillWidth: true
                        placeholderText: "搜索用户ID、邮箱或同意类型..."
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["所有状态", "有效", "已撤销", "即将过期"]
                        currentIndex: 0
                    }

                    Button {
                        text: "导出记录"
                        onClicked:
                        // 导出同意记录
                        {}
                    }
                }

                // 同意记录列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(180, consentListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: consentListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                userId: "user001"
                                email: "<EMAIL>"
                                consentType: "营销推广"
                                grantDate: "2023-06-15"
                                status: "有效"
                                expiryDate: "2024-06-15"
                            }
                            ListElement {
                                userId: "user002"
                                email: "<EMAIL>"
                                consentType: "数据分析"
                                grantDate: "2023-08-20"
                                status: "有效"
                                expiryDate: "2024-08-20"
                            }
                            ListElement {
                                userId: "user003"
                                email: "<EMAIL>"
                                consentType: "营销推广"
                                grantDate: "2023-05-10"
                                status: "已撤销"
                                expiryDate: "2024-05-10"
                            }
                            ListElement {
                                userId: "user004"
                                email: "<EMAIL>"
                                consentType: "个性化服务"
                                grantDate: "2024-01-05"
                                status: "即将过期"
                                expiryDate: "2024-02-05"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: "用户ID"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "邮箱"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "同意类型"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "授权日期"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "过期日期"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: consentDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 35
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: consentDelegateItem.model.userId
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: consentDelegateItem.model.email
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: consentDelegateItem.model.consentType
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: consentDelegateItem.model.grantDate
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: consentDelegateItem.model.status
                                    Layout.preferredWidth: 80
                                    color: consentDelegateItem.model.status === "有效" ? Qt.color("#27ae60") : (consentDelegateItem.model.status === "已撤销" ? Qt.color("#e74c3c") : Qt.color("#f39c12"))
                                }
                                Text {
                                    text: consentDelegateItem.model.expiryDate
                                    Layout.preferredWidth: 100
                                }
                            }
                        }
                    }
                }
            }
        }

        // 数据访问请求处理区域
        GroupBox {
            Layout.fillWidth: true
            title: "数据访问请求处理"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "待处理请求:"
                        font.bold: true
                    }

                    Text {
                        text: "12"
                        color: Qt.color("#e74c3c")
                        font.bold: true
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Button {
                        text: "批量处理"
                        Material.background: Qt.color("#f39c12")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 批量处理请求
                        {}
                    }

                    Button {
                        text: "请求统计"
                        onClicked:
                        // 查看请求统计
                        {}
                    }
                }

                // 访问请求列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(200, requestListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: requestListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                requestId: "REQ001"
                                userEmail: "<EMAIL>"
                                requestType: "数据查看"
                                submitDate: "2024-01-15"
                                status: "待处理"
                                priority: "高"
                            }
                            ListElement {
                                requestId: "REQ002"
                                userEmail: "<EMAIL>"
                                requestType: "数据删除"
                                submitDate: "2024-01-14"
                                status: "处理中"
                                priority: "中"
                            }
                            ListElement {
                                requestId: "REQ003"
                                userEmail: "<EMAIL>"
                                requestType: "数据导出"
                                submitDate: "2024-01-13"
                                status: "已完成"
                                priority: "低"
                            }
                            ListElement {
                                requestId: "REQ004"
                                userEmail: "<EMAIL>"
                                requestType: "数据更正"
                                submitDate: "2024-01-12"
                                status: "待处理"
                                priority: "高"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: "请求ID"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "用户邮箱"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "请求类型"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "提交日期"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "优先级"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "操作"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: accessDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0

                                Text {
                                    text: accessDelegateItem.model.requestId
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: accessDelegateItem.model.userEmail
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: accessDelegateItem.model.requestType
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: accessDelegateItem.model.submitDate
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: accessDelegateItem.model.status
                                    Layout.preferredWidth: 80
                                    color: accessDelegateItem.model.status === "已完成" ? Qt.color("#27ae60") : (accessDelegateItem.model.status === "处理中" ? Qt.color("#f39c12") : Qt.color("#e74c3c"))
                                }
                                Text {
                                    text: accessDelegateItem.model.priority
                                    Layout.preferredWidth: 60
                                    color: accessDelegateItem.model.priority === "高" ? Qt.color("#e74c3c") : (accessDelegateItem.model.priority === "中" ? Qt.color("#f39c12") : Qt.color("#95a5a6"))
                                }
                                RowLayout {
                                    Layout.preferredWidth: 100

                                    Button {
                                        text: "处理"
                                        flat: true
                                        Material.foreground: Qt.color("#3498db")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 合规性检查表区域
        GroupBox {
            Layout.fillWidth: true
            title: "合规性检查表"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                RowLayout {
                    Layout.fillWidth: true

                    ComboBox {
                        Layout.preferredWidth: 200
                        model: ["GDPR检查表", "CCPA检查表", "PIPL检查表", "HIPAA检查表", "自定义检查表"]
                        currentIndex: 2
                    }

                    Button {
                        text: "新建检查表"
                        Material.background: Qt.color("#27ae60")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 新建检查表
                        {}
                    }

                    Button {
                        text: "导出检查表"
                        onClicked:
                        // 导出检查表
                        {}
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "完成度: 78%"
                        font.bold: true
                        color: Qt.color("#f39c12")
                    }
                }

                // 检查项列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(200, complianceListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: complianceListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                item: "数据处理的合法性基础"
                                category: "数据处理"
                                status: "已完成"
                                priority: "高"
                                notes: "已建立合法性基础文档"
                            }
                            ListElement {
                                item: "数据主体权利实施机制"
                                category: "权利保护"
                                status: "部分完成"
                                priority: "高"
                                notes: "需完善删除权实施流程"
                            }
                            ListElement {
                                item: "数据保护影响评估"
                                category: "风险管理"
                                status: "未开始"
                                priority: "中"
                                notes: "计划下月开始"
                            }
                            ListElement {
                                item: "数据处理记录维护"
                                category: "记录管理"
                                status: "已完成"
                                priority: "中"
                                notes: "记录完整且及时更新"
                            }
                            ListElement {
                                item: "第三方数据传输协议"
                                category: "数据传输"
                                status: "部分完成"
                                priority: "高"
                                notes: "部分供应商协议待更新"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: "检查项"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "类别"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "优先级"
                                    font.bold: true
                                    Layout.preferredWidth: 60
                                }
                                Text {
                                    text: "备注"
                                    font.bold: true
                                    Layout.preferredWidth: 150
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: complianceDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 40
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text {
                                    text: complianceDelegateItem.model.item
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: complianceDelegateItem.model.category
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: complianceDelegateItem.model.status
                                    Layout.preferredWidth: 100
                                    color: complianceDelegateItem.model.status === "已完成" ? Qt.color("#27ae60") : (complianceDelegateItem.model.status === "部分完成" ? Qt.color("#f39c12") : Qt.color("#e74c3c"))
                                }
                                Text {
                                    text: complianceDelegateItem.model.priority
                                    Layout.preferredWidth: 60
                                    color: complianceDelegateItem.model.priority === "高" ? Qt.color("#e74c3c") : Qt.color("#95a5a6")
                                }
                                Text {
                                    text: complianceDelegateItem.model.notes
                                    Layout.preferredWidth: 150
                                    wrapMode: Text.WordWrap
                                }
                            }
                        }
                    }
                }
            }
        }

        Item {
            Layout.preferredHeight: 20
        }
    }
}
