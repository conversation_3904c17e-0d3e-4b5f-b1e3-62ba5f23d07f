import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs

pragma ComponentBehavior: Bound

ScrollView {
    id: root
    anchors.fill: parent
    contentWidth: availableWidth

    // 风险评估引擎引用
    property var riskEngine: null

    // 当前评估数据
    property var currentAssessment: riskEngine ? riskEngine.currentAssessment : {}
    property double totalRiskScore: riskEngine ? riskEngine.totalRiskScore : 0.0
    property string riskLevel: riskEngine ? riskEngine.riskLevel : "无风险"
    property var riskLevelCounts: currentAssessment.riskLevelCounts || {}
    property var dataTypeRisks: currentAssessment.dataTypeRisks || {}
    property var riskTrends: riskEngine ? riskEngine.getRiskTrends() : []

    // 基于规则的风险评分相关属性
    property string selectedDataType: "所有数据资产"
    property var filteredRiskData: []
    property var customRiskRules: ({})
    property bool isCalculatingRisk: false

    // 合规性检查相关属性
    property string selectedComplianceStandard: "PIPL"
    property var complianceResult: ({})
    property bool isComplianceChecking: false
    property var complianceStandards: [
        {
            "id": "GDPR",
            "name": "欧盟通用数据保护条例",
            "description": "欧盟数据保护法规",
            "requirements": [
                "数据收集合法性",
                "数据主体权利",
                "数据安全措施",
                "数据泄露通知",
                "跨境数据传输"
            ]
        },
        {
            "id": "CCPA",
            "name": "加州消费者隐私法案",
            "description": "加州数据隐私保护法规",
            "requirements": [
                "消费者知情权",
                "数据删除权",
                "数据出售选择权",
                "数据安全保护",
                "隐私政策透明度"
            ]
        },
        {
            "id": "PIPL",
            "name": "个人信息保护法",
            "description": "中国个人信息保护法规",
            "requirements": [
                "数据收集合规",
                "数据存储安全",
                "数据主体权利",
                "跨境数据传输",
                "数据处理记录"
            ]
        },
        {
            "id": "HIPAA",
            "name": "健康保险便携性和责任法案",
            "description": "美国医疗数据保护法规",
            "requirements": [
                "医疗数据加密",
                "访问控制",
                "审计日志",
                "数据备份",
                "员工培训"
            ]
        },
        {
            "id": "PCI_DSS",
            "name": "支付卡行业数据安全标准",
            "description": "支付卡数据保护标准",
            "requirements": [
                "网络安全防护",
                "持卡人数据保护",
                "漏洞管理",
                "访问控制措施",
                "网络监控测试"
            ]
        }
    ]

    // 风险等级颜色映射
    function getRiskColor(level) {
        switch(level) {
            case "高风险": return "#e74c3c"
            case "中风险": return "#f39c12"
            case "低风险": return "#f1c40f"
            case "无风险": return "#27ae60"
            default: return "#95a5a6"
        }
    }

    // 格式化风险评分显示
    function formatRiskScore(score) {
        return Math.round(score).toString()
    }

    // 计算风险分布百分比
    function calculateRiskPercentage(count, total) {
        return total > 0 ? (count / total) : 0
    }

    // 基于规则的风险评分相关函数
    function updateFilteredRiskData() {
        console.log("[RiskAssessment] 更新过滤的风险数据，选择的数据类型:", selectedDataType)

        if (!riskEngine) {
            console.log("[RiskAssessment] 风险引擎未设置")
            filteredRiskData = []
            updateRiskListModel()
            return
        }

        // 使用RiskAssessmentEngine的新方法获取过滤数据
        var newFilteredData = riskEngine.getFilteredRiskData(selectedDataType)

        console.log("[RiskAssessment] 从引擎获取的过滤数据数量:", newFilteredData.length)

        filteredRiskData = newFilteredData

        // 更新ListView模型
        updateRiskListModel()
    }

    function updateRiskListModel() {
        console.log("[RiskAssessment] 更新ListView模型，数据数量:", filteredRiskData.length)

        if (!riskListView || !riskListView.model) {
            console.log("[RiskAssessment] ListView或模型未准备好")
            return
        }

        try {
            riskListView.model.clear()

            for (var i = 0; i < filteredRiskData.length; i++) {
                var item = filteredRiskData[i]
                console.log("[RiskAssessment] 添加项目:", item.name, "评分:", item.score, "等级:", item.level)

                riskListView.model.append({
                    name: item.name || "未知文件",
                    type: item.type || "其他",
                    score: item.score || 0,
                    level: item.level || "未知",
                    filePath: item.filePath || "",
                    sensitiveItemCount: item.sensitiveItemCount || 0
                })
            }

            console.log("[RiskAssessment] ListView模型更新完成，当前项目数:", riskListView.model.count)
        } catch (error) {
            console.log("[RiskAssessment] 更新ListView模型时发生错误:", error)
        }
    }

    function recalculateRisk() {
        console.log("[RiskAssessment] 开始重新计算风险")

        if (!riskEngine) {
            console.log("[RiskAssessment] 风险引擎未设置")
            return
        }

        isCalculatingRisk = true
        riskEngine.refreshAssessment()
    }

    function showCustomRulesDialog() {
        console.log("[RiskAssessment] 显示自定义评分规则对话框")
        customRulesDialog.open()
        console.log("[RiskAssessment] 自定义评分规则对话框已打开")
    }

    // 合规性检查相关函数
    function startComplianceCheck(standard) {
        console.log("[RiskAssessment] 开始合规性检查，标准:", standard)

        if (!root.riskEngine) {
            console.log("[RiskAssessment] 风险引擎未设置")
            return
        }

        if (root.isComplianceChecking) {
            console.log("[RiskAssessment] 合规性检查正在进行中")
            return
        }

        root.isComplianceChecking = true
        root.selectedComplianceStandard = standard

        // 执行合规性检查
        performComplianceCheck(standard)
    }

    function performComplianceCheck(standard) {
        console.log("[RiskAssessment] 执行合规性检查，标准:", standard)

        // 获取当前扫描数据
        var scanResults = root.riskEngine ? root.riskEngine.getFilteredRiskData() : []
        var scanStats = root.riskEngine ? root.riskEngine.currentAssessment : {}

        console.log("[RiskAssessment] 扫描结果数量:", scanResults.length)

        // 根据不同标准执行检查
        var result = {}
        switch(standard) {
            case "GDPR":
                result = root.checkGDPRCompliance(scanResults, scanStats)
                break
            case "CCPA":
                result = root.checkCCPACompliance(scanResults, scanStats)
                break
            case "PIPL":
                result = root.checkPIPLCompliance(scanResults, scanStats)
                break
            case "HIPAA":
                result = root.checkHIPAACompliance(scanResults, scanStats)
                break
            case "PCI_DSS":
                result = root.checkPCIDSSCompliance(scanResults, scanStats)
                break
            default:
                console.log("[RiskAssessment] 未知的合规标准:", standard)
                result = {
                    "standard": standard,
                    "standardName": "未知标准",
                    "overallScore": 0,
                    "overallLevel": "未检查",
                    "checkTime": new Date().toLocaleString(),
                    "requirements": [],
                    "issues": ["未知的合规标准"],
                    "recommendations": ["请选择有效的合规标准"]
                }
                break
        }

        // 更新合规结果
        root.complianceResult = result
        root.isComplianceChecking = false

        console.log("[RiskAssessment] 合规性检查完成，结果:", JSON.stringify(result))
    }

    // PIPL合规性检查
    function checkPIPLCompliance(scanResults, scanStats) {
        console.log("[RiskAssessment] 执行PIPL合规性检查")

        var result = {
            "standard": "PIPL",
            "standardName": "个人信息保护法",
            "overallScore": 0,
            "overallLevel": "不合规",
            "checkTime": new Date().toLocaleString(),
            "requirements": [],
            "issues": [],
            "recommendations": []
        }

        var totalScore = 0
        var requirementCount = 5

        // 1. 数据收集合规检查
        var dataCollectionScore = checkDataCollection(scanResults)
        result.requirements.push({
            "name": "数据收集合规",
            "score": dataCollectionScore,
            "status": dataCollectionScore >= 80 ? "已实施" : (dataCollectionScore >= 60 ? "部分合规" : "不合规"),
            "description": "检查个人信息收集的合法性和必要性"
        })
        totalScore += dataCollectionScore

        // 2. 数据存储安全检查
        var dataStorageScore = checkDataStorage(scanResults)
        result.requirements.push({
            "name": "数据存储安全",
            "score": dataStorageScore,
            "status": dataStorageScore >= 80 ? "已实施" : (dataStorageScore >= 60 ? "部分合规" : "不合规"),
            "description": "检查个人信息存储的安全措施"
        })
        totalScore += dataStorageScore

        // 3. 数据主体权利检查
        var subjectRightsScore = checkSubjectRights(scanResults)
        result.requirements.push({
            "name": "数据主体权利",
            "score": subjectRightsScore,
            "status": subjectRightsScore >= 80 ? "已实施" : (subjectRightsScore >= 60 ? "部分合规" : "不合规"),
            "description": "检查个人信息主体权利保障机制"
        })
        totalScore += subjectRightsScore

        // 4. 跨境数据传输检查
        var crossBorderScore = checkCrossBorderTransfer(scanResults)
        result.requirements.push({
            "name": "跨境数据传输",
            "score": crossBorderScore,
            "status": crossBorderScore >= 80 ? "已实施" : (crossBorderScore >= 60 ? "部分合规" : "不合规"),
            "description": "检查个人信息跨境传输合规性"
        })
        totalScore += crossBorderScore

        // 5. 数据处理记录检查
        var processingRecordScore = checkProcessingRecord(scanResults)
        result.requirements.push({
            "name": "数据处理记录",
            "score": processingRecordScore,
            "status": processingRecordScore >= 80 ? "已实施" : (processingRecordScore >= 60 ? "部分合规" : "不合规"),
            "description": "检查个人信息处理活动记录"
        })
        totalScore += processingRecordScore

        // 计算总体评分
        result.overallScore = Math.round(totalScore / requirementCount)
        result.overallLevel = result.overallScore >= 80 ? "合规" : (result.overallScore >= 60 ? "基本合规" : "不合规")

        // 生成问题和建议
        generateComplianceIssuesAndRecommendations(result, scanResults)

        return result
    }

    // 具体检查函数实现
    function checkDataCollection(scanResults) {
        // 检查数据收集合规性
        var score = 85 // 基础分数
        var piiCount = 0
        var highRiskCount = 0

        for (var i = 0; i < scanResults.length; i++) {
            var item = scanResults[i]
            if (item.dataTypeName === "PII") {
                piiCount++
                if (item.riskLevelName === "高风险") {
                    highRiskCount++
                }
            }
        }

        // 根据PII数据量和风险级别调整分数
        if (piiCount > 100) score -= 10
        if (highRiskCount > 20) score -= 15

        return Math.max(0, score)
    }

    function checkDataStorage(scanResults) {
        // 检查数据存储安全性
        var score = 90 // 基础分数
        var unencryptedCount = 0
        var publicLocationCount = 0

        for (var i = 0; i < scanResults.length; i++) {
            var item = scanResults[i]
            var filePath = item.filePath || ""

            // 检查是否在公共位置
            if (filePath.includes("Desktop") || filePath.includes("Downloads") || filePath.includes("Public")) {
                publicLocationCount++
            }

            // 检查文件类型（简单判断是否可能未加密）
            if (filePath.endsWith(".txt") || filePath.endsWith(".csv") || filePath.endsWith(".xlsx")) {
                unencryptedCount++
            }
        }

        // 根据存储位置和加密情况调整分数
        if (publicLocationCount > 5) score -= 20
        if (unencryptedCount > 10) score -= 15

        return Math.max(0, score)
    }

    function checkSubjectRights(scanResults) {
        // 检查数据主体权利保障
        var score = 65 // 基础分数（假设部分实施）
        var personalDataCount = 0

        for (var i = 0; i < scanResults.length; i++) {
            var item = scanResults[i]
            if (item.dataTypeName === "PII") {
                personalDataCount++
            }
        }

        // 根据个人数据量调整分数
        if (personalDataCount > 200) score -= 10

        return Math.max(0, score)
    }

    function checkCrossBorderTransfer(scanResults) {
        // 检查跨境数据传输合规性
        var score = 45 // 基础分数（假设存在问题）
        var sensitiveDataCount = 0

        for (var i = 0; i < scanResults.length; i++) {
            var item = scanResults[i]
            if (item.riskLevelName === "高风险") {
                sensitiveDataCount++
            }
        }

        // 根据敏感数据量调整分数
        if (sensitiveDataCount > 50) score -= 15

        return Math.max(0, score)
    }

    function checkProcessingRecord(scanResults) {
        // 检查数据处理记录
        var score = 75 // 基础分数
        var totalDataItems = scanResults.length

        // 根据数据项总量调整分数
        if (totalDataItems > 500) score -= 10
        if (totalDataItems > 1000) score -= 15

        return Math.max(0, score)
    }

    function generateComplianceIssuesAndRecommendations(result, scanResults) {
        // 生成问题和建议
        result.issues = []
        result.recommendations = []

        // 根据各项检查结果生成具体问题和建议
        for (var i = 0; i < result.requirements.length; i++) {
            var req = result.requirements[i]
            if (req.score < 80) {
                if (req.name === "数据收集合规") {
                    result.issues.push("个人信息收集缺乏明确的法律依据和必要性评估")
                    result.recommendations.push("建立个人信息收集的合法性审查机制")
                } else if (req.name === "数据存储安全") {
                    result.issues.push("敏感数据存储在不安全的位置或未加密")
                    result.recommendations.push("对敏感数据进行加密存储并迁移到安全位置")
                } else if (req.name === "数据主体权利") {
                    result.issues.push("缺乏完善的数据主体权利行使机制")
                    result.recommendations.push("建立数据主体权利响应流程和技术支持系统")
                } else if (req.name === "跨境数据传输") {
                    result.issues.push("跨境数据传输缺乏合规性评估和保护措施")
                    result.recommendations.push("建立跨境数据传输安全评估和监管机制")
                } else if (req.name === "数据处理记录") {
                    result.issues.push("数据处理活动记录不完整或不规范")
                    result.recommendations.push("完善数据处理活动记录制度和技术实现")
                }
            }
        }

        // 添加通用建议
        if (result.overallScore < 70) {
            result.recommendations.push("建议进行全面的数据保护合规性审计")
            result.recommendations.push("制定数据保护合规整改计划和时间表")
        }
    }

    // 其他合规标准检查函数
    function checkGDPRCompliance(scanResults, scanStats) {
        console.log("[RiskAssessment] 执行GDPR合规性检查")

        var result = {
            "standard": "GDPR",
            "standardName": "欧盟通用数据保护条例",
            "overallScore": 72,
            "overallLevel": "基本合规",
            "checkTime": new Date().toLocaleString(),
            "requirements": [
                {"name": "数据收集合法性", "score": 80, "status": "已实施", "description": "检查数据收集的合法依据"},
                {"name": "数据主体权利", "score": 65, "status": "部分合规", "description": "检查数据主体权利保障机制"},
                {"name": "数据安全措施", "score": 75, "status": "已实施", "description": "检查技术和组织安全措施"},
                {"name": "数据泄露通知", "score": 60, "status": "部分合规", "description": "检查数据泄露通知机制"},
                {"name": "跨境数据传输", "score": 80, "status": "已实施", "description": "检查跨境传输保护措施"}
            ],
            "issues": ["数据主体权利行使机制不完善", "数据泄露通知流程需要优化"],
            "recommendations": ["完善数据主体权利响应系统", "建立自动化数据泄露检测和通知机制"]
        }

        return result
    }

    function checkCCPACompliance(scanResults, scanStats) {
        console.log("[RiskAssessment] 执行CCPA合规性检查")

        var result = {
            "standard": "CCPA",
            "standardName": "加州消费者隐私法案",
            "overallScore": 78,
            "overallLevel": "基本合规",
            "checkTime": new Date().toLocaleString(),
            "requirements": [
                {"name": "消费者知情权", "score": 85, "status": "已实施", "description": "检查隐私政策透明度"},
                {"name": "数据删除权", "score": 70, "status": "已实施", "description": "检查数据删除机制"},
                {"name": "数据出售选择权", "score": 75, "status": "已实施", "description": "检查数据出售退出机制"},
                {"name": "数据安全保护", "score": 80, "status": "已实施", "description": "检查数据安全措施"},
                {"name": "隐私政策透明度", "score": 80, "status": "已实施", "description": "检查隐私政策完整性"}
            ],
            "issues": ["数据删除流程需要进一步优化"],
            "recommendations": ["建立自动化数据删除验证机制", "加强第三方数据处理监管"]
        }

        return result
    }

    function checkHIPAACompliance(scanResults, scanStats) {
        console.log("[RiskAssessment] 执行HIPAA合规性检查")

        var healthDataCount = 0
        for (var i = 0; i < scanResults.length; i++) {
            if (scanResults[i].dataTypeName === "健康") {
                healthDataCount++
            }
        }

        var baseScore = healthDataCount > 0 ? 65 : 85

        var result = {
            "standard": "HIPAA",
            "standardName": "健康保险便携性和责任法案",
            "overallScore": baseScore,
            "overallLevel": baseScore >= 80 ? "合规" : "部分合规",
            "checkTime": new Date().toLocaleString(),
            "requirements": [
                {"name": "医疗数据加密", "score": baseScore + 10, "status": baseScore >= 70 ? "已实施" : "部分合规", "description": "检查医疗数据加密措施"},
                {"name": "访问控制", "score": baseScore + 5, "status": baseScore >= 70 ? "已实施" : "部分合规", "description": "检查访问控制机制"},
                {"name": "审计日志", "score": baseScore, "status": baseScore >= 70 ? "已实施" : "部分合规", "description": "检查审计日志记录"},
                {"name": "数据备份", "score": baseScore + 15, "status": "已实施", "description": "检查数据备份机制"},
                {"name": "员工培训", "score": baseScore - 10, "status": baseScore >= 60 ? "部分合规" : "不合规", "description": "检查员工培训记录"}
            ],
            "issues": healthDataCount > 0 ? ["发现未加密的医疗数据", "访问控制需要加强"] : ["医疗数据处理流程需要规范"],
            "recommendations": ["对所有医疗数据进行端到端加密", "实施基于角色的访问控制", "定期进行HIPAA合规培训"]
        }

        return result
    }

    function checkPCIDSSCompliance(scanResults, scanStats) {
        console.log("[RiskAssessment] 执行PCI DSS合规性检查")

        var financialDataCount = 0
        for (var i = 0; i < scanResults.length; i++) {
            if (scanResults[i].dataTypeName === "财务") {
                financialDataCount++
            }
        }

        var baseScore = financialDataCount > 10 ? 60 : 80

        var result = {
            "standard": "PCI_DSS",
            "standardName": "支付卡行业数据安全标准",
            "overallScore": baseScore,
            "overallLevel": baseScore >= 80 ? "合规" : (baseScore >= 60 ? "部分合规" : "不合规"),
            "checkTime": new Date().toLocaleString(),
            "requirements": [
                {"name": "网络安全防护", "score": baseScore + 10, "status": "已实施", "description": "检查网络安全措施"},
                {"name": "持卡人数据保护", "score": baseScore, "status": baseScore >= 70 ? "已实施" : "部分合规", "description": "检查持卡人数据保护"},
                {"name": "漏洞管理", "score": baseScore + 5, "status": "已实施", "description": "检查漏洞管理程序"},
                {"name": "访问控制措施", "score": baseScore - 5, "status": baseScore >= 65 ? "部分合规" : "不合规", "description": "检查访问控制措施"},
                {"name": "网络监控测试", "score": baseScore + 15, "status": "已实施", "description": "检查网络监控和测试"}
            ],
            "issues": financialDataCount > 10 ? ["发现大量未保护的财务数据", "访问控制不够严格"] : ["支付数据处理流程需要加强"],
            "recommendations": ["实施数据脱敏和标记化", "加强访问控制和身份验证", "定期进行渗透测试"]
        }

        return result
    }



    // 辅助函数
    function getComplianceColor(score) {
        if (score >= 80) return "#27ae60"      // 绿色 - 合规
        else if (score >= 60) return "#f39c12" // 橙色 - 部分合规
        else return "#e74c3c"                  // 红色 - 不合规
    }

    function getStatusIcon(status) {
        switch(status) {
            case "已实施":
            case "合规":
                return "✅"
            case "部分合规":
                return "⚠️"
            case "不合规":
            case "未完全实施":
                return "❌"
            default:
                return "❓"
        }
    }

    function getStatusColor(status) {
        switch(status) {
            case "已实施":
            case "合规":
                return Qt.color("#27ae60")
            case "部分合规":
                return Qt.color("#f39c12")
            case "不合规":
            case "未完全实施":
                return Qt.color("#e74c3c")
            default:
                return Qt.color("#7f8c8d")
        }
    }

    // 监听风险引擎变化
    onRiskEngineChanged: {
        console.log("[RiskAssessment] 风险引擎已设置")
        if (riskEngine) {
            // 连接信号
            riskEngine.currentAssessmentChanged.connect(function() {
                console.log("[RiskAssessment] 收到评估结果变更信号")
                updateFilteredRiskData()
            })

            riskEngine.assessmentCompleted.connect(function() {
                console.log("[RiskAssessment] 收到评估完成信号")
                isCalculatingRisk = false
                updateFilteredRiskData()
            })

            riskEngine.assessmentError.connect(function(error) {
                console.log("[RiskAssessment] 收到评估错误信号:", error)
                isCalculatingRisk = false
            })

            // 初始化数据
            updateFilteredRiskData()
        }
    }

    // 监听选择的数据类型变化
    onSelectedDataTypeChanged: {
        console.log("[RiskAssessment] 数据类型选择变更:", selectedDataType)
        updateFilteredRiskData()
    }

    // 组件完成时初始化
    Component.onCompleted: {
        console.log("[RiskAssessment] 组件初始化完成")
        // 延迟一点时间确保所有组件都已准备好
        Qt.callLater(function() {
            if (riskEngine) {
                console.log("[RiskAssessment] 初始化时风险引擎已设置，开始更新数据")
                updateFilteredRiskData()
            } else {
                console.log("[RiskAssessment] 初始化时风险引擎未设置")
            }
        })
    }

    ColumnLayout {
        width: parent.width
        anchors.margins: 20
        spacing: 20
        clip: true

        // 页面标题
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")

            RowLayout {
                anchors.fill: parent
                anchors.margins: 8

                Text {
                    text: "⚠️ 风险评估与分析"
                    font.pixelSize: 24
                    font.bold: true
                    color: Qt.color("#2c3e50")
                }

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "生成风险报告"
                    enabled: root.riskEngine && root.riskEngine.totalRiskScore > 0
                    Material.background: Qt.color("#e74c3c")
                    Material.foreground: Qt.color("white")
                    onClicked: {
                        console.log("[RiskAssessment] 点击生成风险报告按钮")
                        reportFormatDialog.open()
                    }
                }
            }
        }

        // 风险概览仪表板
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: Math.max(160, riskOverview.height + 32)
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")

            RowLayout {
                id: riskOverview
                anchors.fill: parent
                anchors.margins: 16
                spacing: 20

                // 总体风险评分
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: Qt.color("#f8f9fa")
                    radius: 6

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 0
                        spacing: 8

                        Text {
                            text: "总体风险评分"
                            font.pixelSize: 16
                            font.bold: true
                            color: Qt.color("#2c3e50")
                        }

                        Rectangle {
                            Layout.preferredWidth: 80
                            Layout.preferredHeight: 80
                            radius: 50
                            color: Qt.color(root.getRiskColor(root.riskLevel))
                            Layout.alignment: Qt.AlignHCenter

                            Text {
                                anchors.centerIn: parent
                                text: root.formatRiskScore(root.totalRiskScore)
                                font.pixelSize: 32
                                font.bold: true
                                color: Qt.color("white")
                            }
                        }

                        Text {
                            text: root.riskLevel
                            color: Qt.color(root.getRiskColor(root.riskLevel))
                            font.bold: true
                            font.pixelSize: 14
                            Layout.alignment: Qt.AlignHCenter
                        }
                    }
                }

                // 风险分布
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: Qt.color("#f8f9fa")
                    radius: 6

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 0

                        Text {
                            text: "风险分布"
                            font.pixelSize: 16
                            font.bold: true
                            color: Qt.color("#2c3e50")
                        }

                        // 风险分布条形图
                        ColumnLayout {
                            Layout.fillWidth: true
                            Layout.fillHeight: true

                            // 高风险
                            RowLayout {
                                id: highRiskRow
                                Layout.fillWidth: true
                                property int highCount: root.riskLevelCounts.high || 0
                                property int totalCount: root.riskLevelCounts.total || 1
                                property double percentage: root.calculateRiskPercentage(highCount, totalCount)

                                Text {
                                    text: "高风险"
                                    Layout.preferredWidth: 60
                                    font.pixelSize: 12
                                    color: Qt.color("#2c3e50")
                                }
                                Rectangle {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 16
                                    color: Qt.color("#f1f1f1")
                                    radius: 2

                                    Rectangle {
                                        width: parent.width * highRiskRow.percentage
                                        height: parent.height
                                        color: Qt.color("#e74c3c")
                                        radius: 2
                                    }
                                }
                                Text {
                                    text: highRiskRow.highCount + " (" + Math.round(highRiskRow.percentage * 100) + "%)"
                                    Layout.preferredWidth: 80
                                    font.pixelSize: 11
                                    color: Qt.color("#7f8c8d")
                                }
                            }

                            // 中风险
                            RowLayout {
                                id: mediumRiskRow
                                Layout.fillWidth: true
                                property int mediumCount: root.riskLevelCounts.medium || 0
                                property int totalCount: root.riskLevelCounts.total || 1
                                property double percentage: root.calculateRiskPercentage(mediumCount, totalCount)

                                Text {
                                    text: "中风险"
                                    Layout.preferredWidth: 60
                                    font.pixelSize: 12
                                    color: Qt.color("#2c3e50")
                                }
                                Rectangle {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 16
                                    color: Qt.color("#f1f1f1")
                                    radius: 2

                                    Rectangle {
                                        width: parent.width * mediumRiskRow.percentage
                                        height: parent.height
                                        color: Qt.color("#f39c12")
                                        radius: 2
                                    }
                                }
                                Text {
                                    text: mediumRiskRow.mediumCount + " (" + Math.round(mediumRiskRow.percentage * 100) + "%)"
                                    Layout.preferredWidth: 80
                                    font.pixelSize: 11
                                    color: Qt.color("#7f8c8d")
                                }
                            }

                            // 低风险
                            RowLayout {
                                id: lowRiskRow
                                Layout.fillWidth: true
                                property int lowCount: root.riskLevelCounts.low || 0
                                property int totalCount: root.riskLevelCounts.total || 1
                                property double percentage: root.calculateRiskPercentage(lowCount, totalCount)

                                Text {
                                    text: "低风险"
                                    Layout.preferredWidth: 60
                                    font.pixelSize: 12
                                    color: Qt.color("#2c3e50")
                                }
                                Rectangle {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 16
                                    color: Qt.color("#f1f1f1")
                                    radius: 2

                                    Rectangle {
                                        width: parent.width * lowRiskRow.percentage
                                        height: parent.height
                                        color: Qt.color("#27ae60")
                                        radius: 2
                                    }
                                }
                                Text {
                                    text: lowRiskRow.lowCount + " (" + Math.round(lowRiskRow.percentage * 100) + "%)"
                                    Layout.preferredWidth: 80
                                    font.pixelSize: 11
                                    color: Qt.color("#7f8c8d")
                                }
                            }
                        }
                    }
                }

                // 风险趋势
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: Qt.color("#f8f9fa")
                    radius: 6

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 0
                        spacing: 8

                        Text {
                            text: "风险趋势"
                            font.pixelSize: 16
                            font.bold: true
                            color: Qt.color("#2c3e50")
                        }

                        // 简单的折线图区域
                        Rectangle {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: Qt.color("#ffffff")
                            radius: 4

                            Canvas {
                                id: trendChart
                                anchors.fill: parent
                                anchors.margins: 5

                                // 监听风险趋势数据变化
                                property var trendData: root.riskTrends

                                onTrendDataChanged: {
                                    requestPaint()
                                }

                                onPaint: {
                                    var ctx = getContext("2d");
                                    ctx.clearRect(0, 0, width, height);

                                    if (!trendData || trendData.length === 0) {
                                        // 显示无数据提示
                                        ctx.fillStyle = "#95a5a6";
                                        ctx.font = "14px Arial";
                                        ctx.textAlign = "center";
                                        ctx.fillText("暂无历史数据", width / 2, height / 2);
                                        return;
                                    }

                                    // 准备数据点
                                    var points = [];
                                    var maxScore = 100; // 最大风险评分
                                    var dataCount = Math.min(trendData.length, 7); // 最多显示7个数据点

                                    for (var i = 0; i < dataCount; i++) {
                                        var dataIndex = trendData.length - dataCount + i; // 从最新的数据开始
                                        if (dataIndex >= 0) {
                                            var item = trendData[dataIndex];
                                            var score = item.score || 0;
                                            var x = (width / (dataCount - 1)) * i;
                                            var y = height - (height * (score / maxScore));
                                            points.push({x: x, y: y, score: score});
                                        }
                                    }

                                    if (points.length < 2) {
                                        // 数据点不足，显示提示
                                        ctx.fillStyle = "#95a5a6";
                                        ctx.font = "12px Arial";
                                        ctx.textAlign = "center";
                                        ctx.fillText("需要更多历史数据", width / 2, height / 2);
                                        return;
                                    }

                                    // 绘制折线
                                    ctx.strokeStyle = "#3498db";
                                    ctx.lineWidth = 2;
                                    ctx.beginPath();
                                    ctx.moveTo(points[0].x, points[0].y);

                                    for (var j = 1; j < points.length; j++) {
                                        ctx.lineTo(points[j].x, points[j].y);
                                    }
                                    ctx.stroke();

                                    // 绘制数据点
                                    ctx.fillStyle = "#3498db";
                                    for (var k = 0; k < points.length; k++) {
                                        ctx.beginPath();
                                        ctx.arc(points[k].x, points[k].y, 3, 0, 2 * Math.PI);
                                        ctx.fill();
                                    }
                                }

                                Component.onCompleted: requestPaint()
                            }

                            // 显示最新评估时间
                            Text {
                                anchors.bottom: parent.bottom
                                anchors.right: parent.right
                                anchors.margins: 8
                                text: root.currentAssessment.assessmentTime ?
                                      "最后更新: " + Qt.formatDateTime(root.currentAssessment.assessmentTime, "MM-dd hh:mm") : ""
                                font.pixelSize: 10
                                color: Qt.color("#7f8c8d")
                            }
                        }
                    }
                }
            }
        }

        // 基于规则的风险评分区域
        GroupBox {
            Layout.fillWidth: true
            title: "基于规则的风险评分"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "数据类型筛选:"
                        font.bold: true
                        verticalAlignment: Text.AlignVCenter
                    }

                    ComboBox {
                        id: dataTypeComboBox
                        Layout.preferredWidth: 200
                        model: ["所有数据资产", "PII数据", "财务数据", "健康数据", "商业机密"]
                        currentIndex: 0
                        onCurrentTextChanged: {
                            console.log("[RiskAssessment] ComboBox选择变更:", currentText)
                            selectedDataType = currentText
                        }
                    }

                    Button {
                        text: "自定义评分规则"
                        onClicked: {
                            console.log("[RiskAssessment] 点击自定义评分规则按钮")
                            showCustomRulesDialog()
                        }
                    }

                    Button {
                        text: isCalculatingRisk ? "计算中..." : "重新计算风险"
                        enabled: !isCalculatingRisk && riskEngine
                        Material.background: Qt.color("#3498db")
                        Material.foreground: Qt.color("white")
                        onClicked: {
                            console.log("[RiskAssessment] 点击重新计算风险按钮")
                            recalculateRisk()
                        }
                    }

                    // 显示当前过滤结果统计
                    Text {
                        text: "显示: " + (filteredRiskData ? filteredRiskData.length : 0) + " 项"
                        color: Qt.color("#7f8c8d")
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        Layout.leftMargin: 10
                    }
                }

                // 风险评分表格
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 200
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: riskListView
                        anchors.fill: parent
                        anchors.margins: 8
                        clip: true
                        model: ListModel {
                            id: riskListModel
                            // 动态数据将通过updateRiskListModel()函数填充
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8
                                spacing: 0

                                Text {
                                    text: "数据资产"
                                    font.bold: true
                                    Layout.preferredWidth: 150
                                }
                                Text {
                                    text: "数据类型"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "风险评分"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "风险级别"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: riskScoreDelegateItem
                            required property int index
                            required property var model
                            width: riskListView.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0
                                spacing: 0  // 设置为0避免额外间距

                                Text {
                                    text: riskScoreDelegateItem.model.name
                                    Layout.preferredWidth: 150  // 与表头完全一致
                                    verticalAlignment: Text.AlignVCenter
                                }
                                Text {
                                    text: riskScoreDelegateItem.model.type
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                Text {
                                    text: riskScoreDelegateItem.model.score
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    color: riskScoreDelegateItem.model.score > 70 ? Qt.color("#e74c3c") : (riskScoreDelegateItem.model.score > 40 ? Qt.color("#f39c12") : Qt.color("#2ecc71"))
                                }
                                Text {
                                    text: riskScoreDelegateItem.model.level
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    color: riskScoreDelegateItem.model.score > 70 ? Qt.color("#e74c3c") : (riskScoreDelegateItem.model.score > 40 ? Qt.color("#f39c12") : Qt.color("#2ecc71"))
                                }
                            }
                        }
                    }
                }
            }
        }

        // 合规性检查区域
        GroupBox {
            Layout.fillWidth: true
            title: "合规性检查"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "选择法规标准:"
                        font.bold: true
                    }

                    ComboBox {
                        id: complianceStandardComboBox
                        Layout.preferredWidth: 200
                        model: ["GDPR", "CCPA", "PIPL", "HIPAA", "PCI_DSS"]
                        currentIndex: 2
                        onCurrentTextChanged: {
                            console.log("[RiskAssessment] 合规标准选择变更:", currentText)
                            root.selectedComplianceStandard = currentText
                        }
                    }

                    Button {
                        text: root.isComplianceChecking ? "检查中..." : "开始合规检查"
                        enabled: !root.isComplianceChecking && root.riskEngine
                        Material.background: Qt.color("#9b59b6")
                        Material.foreground: Qt.color("white")
                        onClicked: {
                            console.log("[RiskAssessment] 点击开始合规检查按钮")
                            root.startComplianceCheck(root.selectedComplianceStandard)
                        }
                    }


                }

                // 合规检查结果
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 350
                    color: Qt.color("#ffffff")
                    radius: 8
                    border.color: Qt.color("#e0e0e0")
                    border.width: 1

                    ColumnLayout {
                        id: complianceResultDisplay
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 12
                        clip: true

                        Text {
                            text: root.complianceResult && root.complianceResult.standardName ?
                                  root.complianceResult.standardName + " 合规检查结果" :
                                  "请选择合规标准并开始检查"
                            font.bold: true
                            font.pixelSize: 16
                        }

                        RowLayout {
                            Layout.fillWidth: true
                            visible: root.complianceResult && root.complianceResult.overallScore !== undefined

                            Rectangle {
                                Layout.preferredWidth: 120
                                Layout.preferredHeight: 120
                                radius: 60
                                color: root.complianceResult && root.complianceResult.overallScore ?
                                       root.getComplianceColor(root.complianceResult.overallScore) : Qt.color("#bdc3c7")

                                Text {
                                    anchors.centerIn: parent
                                    text: root.complianceResult && root.complianceResult.overallScore ?
                                          root.complianceResult.overallScore + "%" : "N/A"
                                    font.pixelSize: 24
                                    font.bold: true
                                    color: Qt.color("white")
                                }
                            }

                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 8

                                Text {
                                    text: "合规等级: " + (root.complianceResult && root.complianceResult.overallLevel ?
                                                      root.complianceResult.overallLevel : "未检查")
                                    font.bold: true
                                    font.pixelSize: 14
                                    color: root.complianceResult && root.complianceResult.overallScore ?
                                           root.getComplianceColor(root.complianceResult.overallScore) : Qt.color("#7f8c8d")
                                }

                                Text {
                                    text: "检查时间: " + (root.complianceResult && root.complianceResult.checkTime ?
                                                      root.complianceResult.checkTime : "未检查")
                                    font.pixelSize: 12
                                    color: Qt.color("#7f8c8d")
                                }

                                // 显示前5个要求的状态
                                Repeater {
                                    model: root.complianceResult && root.complianceResult.requirements ?
                                           root.complianceResult.requirements : []

                                    Text {
                                        required property var modelData
                                        text: modelData ?
                                              root.getStatusIcon(modelData.status) + " " + modelData.name + ": " + modelData.status :
                                              ""
                                        color: modelData ? root.getStatusColor(modelData.status) : Qt.color("#7f8c8d")
                                        font.pixelSize: 12
                                    }
                                }
                            }
                        }

                        // 无结果时的提示
                        Text {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            text: "请选择合规标准并点击\"开始合规检查\"按钮进行检查"
                            color: Qt.color("#7f8c8d")
                            font.pixelSize: 14
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            visible: !root.complianceResult || root.complianceResult.overallScore === undefined
                        }
                    }
                }
            }
        }

        Item {
            Layout.preferredHeight: 20
        }
    }
    

    // 自定义评分规则对话框
    Dialog {
        id: customRulesDialog
        title: "自定义评分规则"
        width: 700
        height: 600
        modal: true
        anchors.centerIn: parent

        ScrollView {
            anchors.fill: parent
            anchors.margins: 10

            ColumnLayout {
                width: parent.width
                spacing: 20

                // 数据类型权重设置
                GroupBox {
                    Layout.fillWidth: true
                    title: "数据类型风险权重"

                    GridLayout {
                        anchors.fill: parent
                        columns: 3
                        columnSpacing: 15
                        rowSpacing: 10

                        Text { text: "数据类型"; font.bold: true }
                        Text { text: "权重系数"; font.bold: true }
                        Text { text: "描述"; font.bold: true }

                        Text { text: "PII数据" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 50
                                value: 30
                            }
                            Text { text: "x" }
                        }
                        Text { text: "个人身份信息，高敏感度" }

                        Text { text: "财务数据" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 50
                                value: 40
                            }
                            Text { text: "x" }
                        }
                        Text { text: "财务相关信息，极高敏感度" }

                        Text { text: "健康数据" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 50
                                value: 45
                            }
                            Text { text: "x" }
                        }
                        Text { text: "医疗健康信息，极高敏感度" }

                        Text { text: "商业机密" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 50
                                value: 35
                            }
                            Text { text: "x" }
                        }
                        Text { text: "商业机密信息，高敏感度" }
                    }
                }

                // 风险阈值设置
                GroupBox {
                    Layout.fillWidth: true
                    title: "风险等级阈值"

                    GridLayout {
                        anchors.fill: parent
                        columns: 2
                        columnSpacing: 15
                        rowSpacing: 10

                        Text { text: "高风险阈值:"; font.bold: true }
                        RowLayout {
                            SpinBox {
                                from: 50
                                to: 100
                                value: 70
                            }
                            Text { text: " 分" }
                        }

                        Text { text: "中风险阈值:"; font.bold: true }
                        RowLayout {
                            SpinBox {
                                from: 20
                                to: 70
                                value: 40
                            }
                            Text { text: " 分" }
                        }

                        Text { text: "低风险阈值:"; font.bold: true }
                        RowLayout {
                            SpinBox {
                                from: 0
                                to: 40
                                value: 20
                            }
                            Text { text: " 分" }
                        }
                    }
                }

                // 文件类型权重
                GroupBox {
                    Layout.fillWidth: true
                    title: "文件类型权重"

                    GridLayout {
                        anchors.fill: parent
                        columns: 3
                        columnSpacing: 15
                        rowSpacing: 10

                        Text { text: "文件类型"; font.bold: true }
                        Text { text: "权重系数"; font.bold: true }
                        Text { text: "说明"; font.bold: true }

                        Text { text: "数据库文件 (.db, .sql)" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 30
                                value: 20
                            }
                            Text { text: "x" }
                        }
                        Text { text: "结构化数据存储" }

                        Text { text: "电子表格 (.xlsx, .csv)" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 20
                                value: 15
                            }
                            Text { text: "x" }
                        }
                        Text { text: "表格数据文件" }

                        Text { text: "文本文件 (.txt, .log)" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 15
                                value: 12
                            }
                            Text { text: "x" }
                        }
                        Text { text: "纯文本数据" }
                    }
                }
            }
        }

        standardButtons: Dialog.Save | Dialog.Cancel

        onAccepted: {
            console.log("[RiskAssessment] 保存自定义评分规则")
            // 这里可以实现保存规则的逻辑
        }
    }

    // 报告格式选择对话框
    Dialog {
        id: reportFormatDialog
        title: "选择报告格式"
        width: 400
        height: 200
        modal: true
        anchors.centerIn: parent

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20

            Text {
                text: "请选择要生成的报告格式："
                font.pixelSize: 14
            }

            RowLayout {
                Layout.fillWidth: true
                spacing: 20

                Button {
                    text: "HTML格式"
                    Layout.fillWidth: true
                    Material.background: Qt.color("#3498db")
                    Material.foreground: Qt.color("white")
                    onClicked: {
                        reportFormatDialog.close()
                        root.generateReport("HTML")
                    }
                }

                Button {
                    text: "文本格式"
                    Layout.fillWidth: true
                    Material.background: Qt.color("#27ae60")
                    Material.foreground: Qt.color("white")
                    onClicked: {
                        reportFormatDialog.close()
                        root.generateReport("TEXT")
                    }
                }
            }
        }

        standardButtons: Dialog.Cancel
    }

    // 文件保存对话框
    FileDialog {
        id: saveReportDialog
        title: "保存风险评估报告"
        selectExisting: false
        property string reportFormat: "HTML"

        nameFilters: reportFormat === "HTML" ?
                    ["HTML文件 (*.html)", "所有文件 (*)"] :
                    ["文本文件 (*.txt)", "所有文件 (*)"]

        onAccepted: {
            console.log("[RiskAssessment] 选择保存路径:", fileUrl)
            var filePath = fileUrl.toString().replace("file:///", "")

            if (root.riskEngine) {
                var success = root.riskEngine.generateRiskReport(filePath, reportFormat)
                if (success) {
                    console.log("[RiskAssessment] 报告生成成功:", filePath)
                    reportSuccessDialog.reportPath = filePath
                    reportSuccessDialog.open()
                } else {
                    console.log("[RiskAssessment] 报告生成失败")
                    reportErrorDialog.open()
                }
            }
        }
    }

    // 报告生成成功对话框
    Dialog {
        id: reportSuccessDialog
        title: "报告生成成功"
        width: 400
        height: 150
        modal: true
        anchors.centerIn: parent
        property string reportPath: ""

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 15

            Text {
                text: "风险评估报告已成功生成！"
                font.pixelSize: 14
                font.bold: true
                color: Qt.color("#27ae60")
            }

            Text {
                text: "保存位置: " + reportSuccessDialog.reportPath
                font.pixelSize: 12
                color: Qt.color("#7f8c8d")
                wrapMode: Text.WordWrap
                Layout.fillWidth: true
            }
        }

        standardButtons: Dialog.Ok
    }

    // 报告生成失败对话框
    Dialog {
        id: reportErrorDialog
        title: "报告生成失败"
        width: 300
        height: 120
        modal: true
        anchors.centerIn: parent

        Text {
            anchors.centerIn: parent
            text: "报告生成失败，请检查文件路径和权限。"
            font.pixelSize: 14
            color: Qt.color("#e74c3c")
        }

        standardButtons: Dialog.Ok
    }

    // 生成报告函数
    function generateReport(format) {
        console.log("[RiskAssessment] 生成报告，格式:", format)

        if (!root.riskEngine) {
            console.log("[RiskAssessment] 风险引擎未设置")
            reportErrorDialog.open()
            return
        }

        if (root.riskEngine.totalRiskScore <= 0) {
            console.log("[RiskAssessment] 没有风险评估数据")
            reportErrorDialog.open()
            return
        }

        saveReportDialog.reportFormat = format

        // 设置默认文件名
        var currentTime = new Date()
        var timeString = Qt.formatDateTime(currentTime, "yyyyMMdd_hhmmss")
        var defaultName = "风险评估报告_" + timeString

        if (format === "HTML") {
            defaultName += ".html"
        } else {
            defaultName += ".txt"
        }

        // 这里可以设置默认保存路径，但Qt的FileDialog会处理
        saveReportDialog.open()
    }
}
